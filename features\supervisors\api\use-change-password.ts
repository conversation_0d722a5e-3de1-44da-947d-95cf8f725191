import { useMutation, useQueryClient } from "@tanstack/react-query"
import { Supervisor } from "../server/Supervisor";
import { toast } from "sonner";


export const useChangePassword = () => {

    const queryClient = useQueryClient();

    const mutation = useMutation({
        mutationFn: async (data: {newPassword:string}) => {
            const res = await Supervisor.changePassword(data.newPassword);
            return res;
        },
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ["supervisors"] });
        },
        onError: (error) => {
            toast.error(error.message)
        }
    });

    return mutation;
}