"use client"

import * as React from "react"
import { ChevronsUpDown, GalleryVerticalEnd, Plus } from "lucide-react"

import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuShortcut,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import {
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  useSidebar,
} from "@/components/ui/sidebar"
import { useIptFormModal } from "@/features/ipt/hooks/use-ipt-form-modal"
import { useFetchIPTs } from "@/features/ipt/api/use-fetch-ipts"
import { useGlobalManager } from "@/hooks/use-context"

const teams = [
  {
    name: "No IPT",
  },
]

export interface IPT {
  id: string
  name: string
  isDefault: boolean
}

export function TeamSwitcher() {
  const ipt = useIptFormModal()
  const { changeIptId , user } = useGlobalManager()

  const { data : ipts } = useFetchIPTs()

  const { isMobile } = useSidebar()
  const [activeTeam, setActiveTeam] = React.useState<IPT | { name: string }>(
    ipts && ipts.length > 0 ? ipts[0] : teams[0]
  )

  React.useEffect(() => {
    if (ipts && ipts.length > 0) {
      setActiveTeam(ipts[0]);
      changeIptId({ data: { id: ipts[0].id } })
    }
  }, [ipts])

  if (!activeTeam) {
    return null
  }

  return (
    <SidebarMenu>
      <SidebarMenuItem>
        {user?.role == "admin" ? (
          
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <SidebarMenuButton
                size="lg"
                className="data-[state=open]:bg-sidebar-accent data-[state=open]:text-sidebar-accent-foreground"
              >
                <div className="flex aspect-square size-8 items-center justify-center rounded-lg bg-sidebar-primary text-sidebar-primary-foreground">
                  <GalleryVerticalEnd className="size-4" />
                </div>
                <div className="grid flex-1 text-left text-sm leading-tight">
                  <span className="truncate font-semibold">
                  DIT IPTMS
                  </span>
                  <span className="truncate text-xs">{activeTeam.name}</span>
                </div>
                <ChevronsUpDown className="ml-auto" />
              </SidebarMenuButton>
            </DropdownMenuTrigger>
            <DropdownMenuContent
              className="w-[--radix-dropdown-menu-trigger-width] min-w-56 rounded-lg"
              align="start"
              side={isMobile ? "bottom" : "right"}
              sideOffset={4}
            >
              <DropdownMenuLabel className="text-xs text-muted-foreground">
                IPT(s)
              </DropdownMenuLabel>
              {ipts && ipts.length > 0 ? (
                ipts.map((ipt : IPT) => (
                  <DropdownMenuItem
                    key={ipt.id}
                    className="gap-2 p-2"
                    onClick={() => {
                      setActiveTeam(ipt);
                      changeIptId({ data: { id: ipt.id } });
                    }}
                  >
                    <div className="flex aspect-square size-6 items-center justify-center rounded-md border bg-background">
                      <GalleryVerticalEnd className="size-4" />
                    </div>
                    <div className="font-medium text-muted-foreground">
                      {ipt.name}
                    </div>
                    <DropdownMenuShortcut>
                      {ipt.isDefault && "Default"}
                    </DropdownMenuShortcut>
                  </DropdownMenuItem>
                ))
              ) : (
                <div className="flex items-center justify-center p-4 text-sm text-muted-foreground">
                  No IPTs found
                </div>
              )}
              <DropdownMenuSeparator />
              <DropdownMenuItem className="gap-2 p-2" onClick={ipt.open}>
                <div className="flex size-6 items-center justify-center rounded-md border bg-background">
                  <Plus className="size-4" />
                </div>
                <div className="font-medium text-muted-foreground">Add Ipt</div>
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        ) : (
          <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <SidebarMenuButton
              size="lg"
              className="data-[state=open]:bg-sidebar-accent data-[state=open]:text-sidebar-accent-foreground"
            >
              <div className="flex aspect-square size-8 items-center justify-center rounded-lg bg-sidebar-primary text-sidebar-primary-foreground">
                <GalleryVerticalEnd className="size-4" />
              </div>
              <div className="grid flex-1 text-left text-sm leading-tight">
                <span className="truncate font-semibold">
                 DIT IPTMS
                </span>
                <span className="truncate text-xs">{activeTeam.name}</span>
              </div>
            </SidebarMenuButton>
          </DropdownMenuTrigger>
        </DropdownMenu>
        )}
      </SidebarMenuItem>
    </SidebarMenu>
  )
}
