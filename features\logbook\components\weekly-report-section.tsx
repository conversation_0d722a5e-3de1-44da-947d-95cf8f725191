"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Textarea } from "@/components/ui/textarea"
import { Badge } from "@/components/ui/badge"
import { WeeklyReport } from "../types/logbook"
import { useGlobalManager } from "@/hooks/use-context"
import {
  FileText,
  Edit,
  Save,
  X,
  Plus,
  BarChart3
} from "lucide-react"

interface WeeklyReportSectionProps {
  weekNumber: number
  weeklyReport?: WeeklyReport | null
  onSaveWeeklyReport: (report: WeeklyReport) => Promise<void>
  loading?: boolean
}

export function WeeklyReportSection({
  weekNumber,
  weeklyReport,
  onSaveWeeklyReport,
  loading = false
}: WeeklyReportSectionProps) {
  const { user } = useGlobalManager()
  const [isEditing, setIsEditing] = useState(false)
  const [reportData, setReportData] = useState<Partial<WeeklyReport>>(
    weeklyReport || {}
  )
  const [saving, setSaving] = useState(false)

  // Calculate week dates
  const getWeekDates = () => {
    const now = new Date()
    const currentWeek = Math.ceil((now.getTime() - new Date(now.getFullYear(), 0, 1).getTime()) / (7 * 24 * 60 * 60 * 1000))
    const weekDiff = weekNumber - currentWeek
    const weekStart = new Date(now)
    weekStart.setDate(now.getDate() - now.getDay() + 1 + (weekDiff * 7)) // Monday
    
    const weekEnd = new Date(weekStart)
    weekEnd.setDate(weekStart.getDate() + 5) // Saturday
    
    return {
      start: weekStart.toISOString().split('T')[0],
      end: weekEnd.toISOString().split('T')[0]
    }
  }

  const handleEdit = () => {
    setIsEditing(true)
    setReportData(weeklyReport || {})
  }

  const handleCancel = () => {
    setIsEditing(false)
    setReportData(weeklyReport || {})
  }

  const handleSave = async () => {
    if (!reportData.summary || reportData.summary.trim().length < 100) {
      alert('Weekly report summary must be at least 100 characters long.')
      return
    }

    try {
      setSaving(true)
      const weekDates = getWeekDates()
      
      const report: WeeklyReport = {
        ...reportData,
        student_id: user?.id || "",
        week_number: weekNumber,
        week_start_date: weekDates.start,
        week_end_date: weekDates.end,
        summary: reportData.summary || "",
        status: 'draft',
        is_submitted: false
      } as WeeklyReport

      await onSaveWeeklyReport(report)
      setIsEditing(false)
    } catch (error) {
      console.error('Failed to save weekly report:', error)
    } finally {
      setSaving(false)
    }
  }

  const handleSubmit = async () => {
    if (!reportData.summary || reportData.summary.trim().length < 100) {
      alert('Weekly report summary must be at least 100 characters long.')
      return
    }

    try {
      setSaving(true)
      const weekDates = getWeekDates()
      
      const report: WeeklyReport = {
        ...reportData,
        student_id: user?.id || "",
        week_number: weekNumber,
        week_start_date: weekDates.start,
        week_end_date: weekDates.end,
        summary: reportData.summary || "",
        status: 'submitted',
        is_submitted: true,
        submitted_at: new Date().toISOString()
      } as WeeklyReport

      await onSaveWeeklyReport(report)
      setIsEditing(false)
    } catch (error) {
      console.error('Failed to submit weekly report:', error)
    } finally {
      setSaving(false)
    }
  }

  const updateReportData = (field: keyof WeeklyReport, value: any) => {
    setReportData({ ...reportData, [field]: value })
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            <BarChart3 className="h-5 w-5" />
            Weekly Report - Week {weekNumber}
          </CardTitle>
          {weeklyReport && !isEditing && (
            <div className="flex items-center gap-2">
              <Badge variant={weeklyReport.is_submitted ? "default" : "secondary"}>
                {weeklyReport.is_submitted ? "Submitted" : "Draft"}
              </Badge>
              <Button
                variant="outline"
                size="sm"
                onClick={handleEdit}
                disabled={loading}
              >
                <Edit className="h-4 w-4 mr-2" />
                Edit
              </Button>
            </div>
          )}
        </div>
      </CardHeader>
      <CardContent>
        {loading ? (
          <div className="flex items-center justify-center h-32">
            <div className="text-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
              <p className="text-muted-foreground">Loading weekly report...</p>
            </div>
          </div>
        ) : isEditing || !weeklyReport ? (
          <div className="space-y-6">
            <div className="space-y-2">
              <label className="text-sm font-medium">
                Weekly Summary <span className="text-destructive">*</span>
              </label>
              <Textarea
                placeholder="Write a comprehensive summary of your week's activities, achievements, challenges, and learnings. Minimum 100 characters required."
                value={reportData.summary || ""}
                onChange={(e) => updateReportData('summary', e.target.value)}
                rows={8}
                className="min-h-[200px]"
              />
              <div className="text-xs text-muted-foreground">
                {(reportData.summary || "").length} / 100 characters minimum
              </div>
            </div>

            <div className="space-y-2">
              <label className="text-sm font-medium">Objectives Achieved</label>
              <Textarea
                placeholder="Describe the objectives and goals you achieved this week..."
                value={reportData.objectives_achieved || ""}
                onChange={(e) => updateReportData('objectives_achieved', e.target.value)}
                rows={3}
              />
            </div>

            <div className="space-y-2">
              <label className="text-sm font-medium">Challenges Encountered</label>
              <Textarea
                placeholder="Describe any challenges or difficulties you faced this week..."
                value={reportData.challenges_encountered || ""}
                onChange={(e) => updateReportData('challenges_encountered', e.target.value)}
                rows={3}
              />
            </div>

            <div className="space-y-2">
              <label className="text-sm font-medium">Skills Developed</label>
              <Textarea
                placeholder="Describe new skills you learned or existing skills you improved..."
                value={reportData.skills_developed || ""}
                onChange={(e) => updateReportData('skills_developed', e.target.value)}
                rows={3}
              />
            </div>

            <div className="space-y-2">
              <label className="text-sm font-medium">Learning Outcomes</label>
              <Textarea
                placeholder="Describe key learnings and insights from this week..."
                value={reportData.learning_outcomes || ""}
                onChange={(e) => updateReportData('learning_outcomes', e.target.value)}
                rows={3}
              />
            </div>

            <div className="space-y-2">
              <label className="text-sm font-medium">Areas for Improvement</label>
              <Textarea
                placeholder="Identify areas where you can improve or need more focus..."
                value={reportData.areas_for_improvement || ""}
                onChange={(e) => updateReportData('areas_for_improvement', e.target.value)}
                rows={3}
              />
            </div>

            <div className="space-y-2">
              <label className="text-sm font-medium">Next Week Goals</label>
              <Textarea
                placeholder="Set goals and objectives for the upcoming week..."
                value={reportData.next_week_goals || ""}
                onChange={(e) => updateReportData('next_week_goals', e.target.value)}
                rows={3}
              />
            </div>

            <div className="flex items-center gap-2">
              <Button
                onClick={handleSave}
                disabled={saving || !reportData.summary || (reportData.summary || "").length < 100}
                className="flex items-center gap-2"
              >
                <Save className="h-4 w-4" />
                Save Draft
              </Button>
              <Button
                onClick={handleSubmit}
                disabled={saving || !reportData.summary || (reportData.summary || "").length < 100}
                variant="default"
                className="flex items-center gap-2"
              >
                <FileText className="h-4 w-4" />
                Submit Report
              </Button>
              {weeklyReport && (
                <Button
                  variant="outline"
                  onClick={handleCancel}
                  disabled={saving}
                >
                  <X className="h-4 w-4 mr-2" />
                  Cancel
                </Button>
              )}
            </div>
          </div>
        ) : (
          <div className="space-y-4">
            <div>
              <h4 className="font-medium mb-2">Weekly Summary</h4>
              <div className="p-4 bg-muted rounded-lg">
                <p className="text-sm whitespace-pre-wrap">{weeklyReport.summary}</p>
              </div>
            </div>

            {weeklyReport.objectives_achieved && (
              <div>
                <h4 className="font-medium mb-2">Objectives Achieved</h4>
                <div className="p-4 bg-muted rounded-lg">
                  <p className="text-sm whitespace-pre-wrap">{weeklyReport.objectives_achieved}</p>
                </div>
              </div>
            )}

            {weeklyReport.challenges_encountered && (
              <div>
                <h4 className="font-medium mb-2">Challenges Encountered</h4>
                <div className="p-4 bg-muted rounded-lg">
                  <p className="text-sm whitespace-pre-wrap">{weeklyReport.challenges_encountered}</p>
                </div>
              </div>
            )}

            {weeklyReport.skills_developed && (
              <div>
                <h4 className="font-medium mb-2">Skills Developed</h4>
                <div className="p-4 bg-muted rounded-lg">
                  <p className="text-sm whitespace-pre-wrap">{weeklyReport.skills_developed}</p>
                </div>
              </div>
            )}

            {weeklyReport.learning_outcomes && (
              <div>
                <h4 className="font-medium mb-2">Learning Outcomes</h4>
                <div className="p-4 bg-muted rounded-lg">
                  <p className="text-sm whitespace-pre-wrap">{weeklyReport.learning_outcomes}</p>
                </div>
              </div>
            )}

            {weeklyReport.areas_for_improvement && (
              <div>
                <h4 className="font-medium mb-2">Areas for Improvement</h4>
                <div className="p-4 bg-muted rounded-lg">
                  <p className="text-sm whitespace-pre-wrap">{weeklyReport.areas_for_improvement}</p>
                </div>
              </div>
            )}

            {weeklyReport.next_week_goals && (
              <div>
                <h4 className="font-medium mb-2">Next Week Goals</h4>
                <div className="p-4 bg-muted rounded-lg">
                  <p className="text-sm whitespace-pre-wrap">{weeklyReport.next_week_goals}</p>
                </div>
              </div>
            )}

            {weeklyReport.submitted_at && (
              <div className="text-sm text-muted-foreground">
                Submitted on: {new Date(weeklyReport.submitted_at).toLocaleDateString()}
              </div>
            )}
          </div>
        )}

        {!weeklyReport && !isEditing && (
          <div className="text-center py-8">
            <FileText className="h-12 w-12 mx-auto mb-4 text-muted-foreground opacity-50" />
            <h3 className="text-lg font-medium mb-2">No weekly report for Week {weekNumber}</h3>
            <p className="text-muted-foreground mb-4">Create your weekly summary report</p>
            <Button onClick={() => setIsEditing(true)}>
              <Plus className="h-4 w-4 mr-2" />
              Create Weekly Report
            </Button>
          </div>
        )}
      </CardContent>
    </Card>
  )
}
