'use client'

import { useState, useEffect } from "react"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Ta<PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { 
  Users, 
  FileText, 
  MessageSquare, 
  BookOpen,
  Download,
  TrendingUp,
  Clock,
  CheckCircle2
} from "lucide-react"
import { Reports } from "@/features/reports/server/Reports"
import { StudentReportSummary } from "@/features/reports/types/reports"
import { toast } from "sonner"

export default function AdminReportsOverviewPage() {
  const [loading, setLoading] = useState(true)
  const [summaryData, setSummaryData] = useState<StudentReportSummary[]>([])
  const [stats, setStats] = useState({
    totalStudents: 0,
    completedLogbooks: 0,
    submittedFinalReports: 0,
    totalFeedback: 0,
    averageCompletion: 0
  })

  useEffect(() => {
    loadOverviewData()
  }, [])

  const loadOverviewData = async () => {
    setLoading(true)
    try {
      const result = await Reports.getStudentReportsSummary()
      const data = result?.data || []
      setSummaryData(data)
      
      // Calculate statistics
      const totalStudents = data.length
      const completedLogbooks = data.filter((s: StudentReportSummary) => s.weeks_completed >= 10).length
      const submittedFinalReports = data.filter((s: StudentReportSummary) => s.final_report_status === 'submitted').length
      const totalFeedback = data.reduce((sum: number, s: StudentReportSummary) => sum + s.feedback_count, 0)
      const averageCompletion = totalStudents > 0 
        ? data.reduce((sum: number, s: StudentReportSummary) => sum + s.overall_completion, 0) / totalStudents 
        : 0

      setStats({
        totalStudents,
        completedLogbooks,
        submittedFinalReports,
        totalFeedback,
        averageCompletion
      })
    } catch (error) {
      console.error("Failed to load overview data:", error)
    } finally {
      setLoading(false)
    }
  }

  const handleExport = async (type: 'logbooks' | 'final-reports' | 'feedback', format: 'csv' | 'excel' | 'pdf') => {
    try {
      await Reports.exportReports(type, format)
    } catch (error) {
      toast.error("Failed to export reports")
    }
  }

  if (loading) {
    return (
      <div className="h-full flex-1 flex-col space-y-4 p-4 md:space-y-8 md:p-8 flex">
        <div className="flex items-center justify-center h-40">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
            <p className="text-muted-foreground">Loading reports overview...</p>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="h-full flex-1 flex-col space-y-4 p-4 md:space-y-8 md:p-8 flex">
      <div className="flex items-center justify-between space-y-2">
        <div>
          <h2 className="text-2xl font-bold tracking-tight">Reports Overview</h2>
          <p className="text-muted-foreground">
            Comprehensive view of all student reports and submissions
          </p>
        </div>
        
        <div className="flex items-center space-x-2">
          <Button variant="outline" onClick={() => handleExport('logbooks', 'excel')}>
            <Download className="mr-2 h-4 w-4" />
            Export All
          </Button>
        </div>
      </div>

      {/* Statistics Cards */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-5">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Students</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.totalStudents}</div>
            <p className="text-xs text-muted-foreground">
              Active in program
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Completed Logbooks</CardTitle>
            <BookOpen className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.completedLogbooks}</div>
            <p className="text-xs text-muted-foreground">
              {stats.totalStudents > 0 ? Math.round((stats.completedLogbooks / stats.totalStudents) * 100) : 0}% completion rate
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Final Reports</CardTitle>
            <FileText className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.submittedFinalReports}</div>
            <p className="text-xs text-muted-foreground">
              {stats.totalStudents > 0 ? Math.round((stats.submittedFinalReports / stats.totalStudents) * 100) : 0}% submitted
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Feedback</CardTitle>
            <MessageSquare className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.totalFeedback}</div>
            <p className="text-xs text-muted-foreground">
              Feedback submissions
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Avg. Completion</CardTitle>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{Math.round(stats.averageCompletion)}%</div>
            <p className="text-xs text-muted-foreground">
              Overall progress
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Export Options */}
      <Card>
        <CardHeader>
          <CardTitle>Export Reports</CardTitle>
        </CardHeader>
        <CardContent>
          <Tabs defaultValue="logbooks" className="w-full">
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="logbooks">Logbooks</TabsTrigger>
              <TabsTrigger value="final-reports">Final Reports</TabsTrigger>
              <TabsTrigger value="feedback">Feedback</TabsTrigger>
            </TabsList>
            
            <TabsContent value="logbooks" className="space-y-4">
              <p className="text-sm text-muted-foreground">
                Export all student logbook data including daily logs and weekly reports
              </p>
              <div className="flex items-center space-x-2">
                <Button variant="outline" onClick={() => handleExport('logbooks', 'csv')}>
                  <Download className="mr-2 h-4 w-4" />
                  CSV
                </Button>
                <Button variant="outline" onClick={() => handleExport('logbooks', 'excel')}>
                  <Download className="mr-2 h-4 w-4" />
                  Excel
                </Button>
                <Button variant="outline" onClick={() => handleExport('logbooks', 'pdf')}>
                  <Download className="mr-2 h-4 w-4" />
                  PDF
                </Button>
              </div>
            </TabsContent>
            
            <TabsContent value="final-reports" className="space-y-4">
              <p className="text-sm text-muted-foreground">
                Export all final report submissions and metadata
              </p>
              <div className="flex items-center space-x-2">
                <Button variant="outline" onClick={() => handleExport('final-reports', 'csv')}>
                  <Download className="mr-2 h-4 w-4" />
                  CSV
                </Button>
                <Button variant="outline" onClick={() => handleExport('final-reports', 'excel')}>
                  <Download className="mr-2 h-4 w-4" />
                  Excel
                </Button>
                <Button variant="outline" onClick={() => handleExport('final-reports', 'pdf')}>
                  <Download className="mr-2 h-4 w-4" />
                  PDF
                </Button>
              </div>
            </TabsContent>
            
            <TabsContent value="feedback" className="space-y-4">
              <p className="text-sm text-muted-foreground">
                Export all student feedback and recommendations
              </p>
              <div className="flex items-center space-x-2">
                <Button variant="outline" onClick={() => handleExport('feedback', 'csv')}>
                  <Download className="mr-2 h-4 w-4" />
                  CSV
                </Button>
                <Button variant="outline" onClick={() => handleExport('feedback', 'excel')}>
                  <Download className="mr-2 h-4 w-4" />
                  Excel
                </Button>
                <Button variant="outline" onClick={() => handleExport('feedback', 'pdf')}>
                  <Download className="mr-2 h-4 w-4" />
                  PDF
                </Button>
              </div>
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>

      {/* Recent Activity */}
      <Card>
        <CardHeader>
          <CardTitle>Recent Student Activity</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {summaryData.slice(0, 5).map((student) => (
              <div key={student.student_id} className="flex items-center justify-between p-3 border rounded-lg">
                <div className="flex items-center space-x-3">
                  <div className="w-8 h-8 bg-primary/10 rounded-full flex items-center justify-center">
                    <Users className="h-4 w-4 text-primary" />
                  </div>
                  <div>
                    <p className="font-medium">{student.student_name}</p>
                    <p className="text-sm text-muted-foreground">{student.student_email}</p>
                  </div>
                </div>
                
                <div className="flex items-center space-x-4 text-sm">
                  <div className="flex items-center space-x-1">
                    <BookOpen className="h-4 w-4" />
                    <span>{student.weeks_completed}/10 weeks</span>
                  </div>
                  
                  <div className="flex items-center space-x-1">
                    {student.final_report_status === 'submitted' ? (
                      <CheckCircle2 className="h-4 w-4 text-green-500" />
                    ) : (
                      <Clock className="h-4 w-4 text-orange-500" />
                    )}
                    <span className="capitalize">{student.final_report_status.replace('_', ' ')}</span>
                  </div>
                  
                  <div className="flex items-center space-x-1">
                    <MessageSquare className="h-4 w-4" />
                    <span>{student.feedback_count} feedback</span>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
