import axios from "axios";
import { toast } from "sonner";

export class Auth {
  public static api_url = process.env.NEXT_PUBLIC_API_URL;
  
  public static async login(email: string, password: string) {
    try {
      const response = await axios.post(`${this.api_url}/auth/login`, {
        email,
        password,
      });

      if (response.status === 200) {
        toast.success(response.data.message);
        return response.data; 
      }
    } catch (error: any) {
      console.log(error);
      throw error.response?.data || error.message; 
    }
  }

  public static async register(formData: {
    email: string;
    password: string;
    fname: string;
    mname: string;
    lname: string;
    regno: string;
    class: string;
    program_id: string;
    ipt_id: string;
  }) {
    try {
      const response = await axios.post(`${this.api_url}/auth/register`, formData);

      if (response.status === 200) {
        toast.success(response.data.message);
        return response.data;
      }
    } catch (error: any) {
      throw error.response.data;
    }
  }

  public static async getUser(token: string) {
    try {
      const response = await axios.get(`${this.api_url}/auth/getLoggedInUser`,
        {
          headers: {
            Authorization: token,
          },
        }
      );

      if (response.status === 200) {
        toast.success(response.data.message);
        return response.data; 
      }
    } catch (error: any) {
      throw error.response.data; 
    }
  }
  
  public static async updateProfile(userData: {
    email: string;
    fname: string;
    lname: string;
    phone: string;
  }) {
    const token = localStorage.getItem('iptms_token');
    
    try {
      const response = await axios.patch(
        `${this.api_url}/auth/updateProfile`,
        userData,
        {
          headers: {
            Authorization: token,
          },
        }
      );

      if (response.status === 200) {
        toast.success(response.data.message);
        return response.data;
      }
    } catch (error: any) {
      toast.error(error.response?.data?.message || "Failed to update profile");
      throw error.response?.data || error.message;
    }
  }
}
