import { useQuery } from "@tanstack/react-query"
import { Supervisor } from "../server/Supervisor";
import { toast } from "sonner";

export const useFetchSupervisor = () =>{
    const query = useQuery({
        queryKey:['supervisors'],
        queryFn: async () => {
            try {
                const res = await Supervisor.index();

                if(res.success){

                    return res.data;
                }
                return []
            } catch (error: any) {
                toast.error(error.message)
                return []
            }
        }
    })

    return query;
}