'use client'

import { useState, useEffect } from "react"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from "@/components/ui/select"
import { Badge } from "@/components/ui/badge"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import {
  Download,
  Search,
  Filter,
  BookOpen,
  Calendar,
  User,
  Clock,
  CheckCircle2,
  AlertCircle,
  Eye,
  FileText,
  BarChart3
} from "lucide-react"
import { IPTLogbooks } from "@/features/logbook/server/IPTLogbooks"
import { toast } from "sonner"

export default function AdminLogbooksPage() {
  const [loading, setLoading] = useState(true)
  const [dailyLogs, setDailyLogs] = useState<any[]>([])
  const [weeklyReports, setWeeklyReports] = useState<any[]>([])
  const [statistics, setStatistics] = useState<any>(null)
  const [filters, setFilters] = useState({
    student_id: '',
    week_number: '',
    status: 'all'
  })
  const [activeTab, setActiveTab] = useState('daily-logs')

  useEffect(() => {
    loadData()
  }, [filters, activeTab])

  const loadData = async () => {
    try {
      setLoading(true)

      if (activeTab === 'daily-logs') {
        const result = await IPTLogbooks.getAllDailyLogs(filters)
        setDailyLogs(result?.data || [])
      } else if (activeTab === 'weekly-reports') {
        const result = await IPTLogbooks.getAllWeeklyReports(filters)
        setWeeklyReports(result?.data || [])
      }

      // Load statistics
      const statsResult = await IPTLogbooks.getLogbookStatistics()
      setStatistics(statsResult?.data || null)

    } catch (error) {
      console.error("Failed to load data:", error)
    } finally {
      setLoading(false)
    }
  }

  const handleExport = async () => {
    try {
      // This would typically generate and download a CSV/Excel file
      toast.info("Export functionality to be implemented")
    } catch (error) {
      console.error("Failed to export logbooks:", error)
    }
  }

  const handleFilterChange = (key: string, value: string) => {
    setFilters(prev => ({
      ...prev,
      [key]: value === 'all' ? '' : value
    }))
  }

  const getCompletionRate = () => {
    if (!statistics || statistics.total_daily_logs === 0) return 0
    return Math.round((statistics.submitted_daily_logs / statistics.total_daily_logs) * 100)
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    })
  }

  return (
    <div className="flex flex-1 flex-col">
      <div className="@container/main flex flex-1 flex-col gap-2">
        <div className="flex flex-col gap-4 py-4 md:gap-6 md:py-6">
          <div className="flex flex-col gap-4 px-4 lg:px-6">
            <div className="flex items-center justify-between">
              <div className="flex flex-col gap-2">
                <h1 className="text-2xl font-bold">Logbook Management</h1>
                <p className="text-muted-foreground">
                  Monitor and manage all student logbook entries and weekly reports
                </p>
              </div>
              <div className="flex items-center gap-2">
                <Button variant="outline" onClick={handleExport} className="flex items-center gap-2">
                  <Download className="h-4 w-4" />
                  Export Data
                </Button>
              </div>
            </div>

            {/* Statistics Overview */}
            {statistics && (
              <div className="grid gap-4 md:grid-cols-4">
                <Card>
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle className="text-sm font-medium">Total Students</CardTitle>
                    <User className="h-4 w-4 text-muted-foreground" />
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">{statistics.total_students || 0}</div>
                    <p className="text-xs text-muted-foreground">
                      Registered students
                    </p>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle className="text-sm font-medium">Daily Logs</CardTitle>
                    <BookOpen className="h-4 w-4 text-blue-600" />
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">{statistics.submitted_daily_logs || 0}/{statistics.total_daily_logs || 0}</div>
                    <p className="text-xs text-muted-foreground">
                      {getCompletionRate()}% submitted
                    </p>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle className="text-sm font-medium">Weekly Reports</CardTitle>
                    <FileText className="h-4 w-4 text-green-600" />
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">{statistics.submitted_weekly_reports || 0}/{statistics.total_weekly_reports || 0}</div>
                    <p className="text-xs text-muted-foreground">
                      Reports submitted
                    </p>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle className="text-sm font-medium">Avg. Hours/Day</CardTitle>
                    <Clock className="h-4 w-4 text-orange-600" />
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">{statistics.average_hours_per_day?.toFixed(1) || '0.0'}</div>
                    <p className="text-xs text-muted-foreground">
                      Current week: {statistics.current_week || 1}
                    </p>
                  </CardContent>
                </Card>
              </div>
            )}

            {/* Filters */}
            <Card>
              <CardContent className="p-4">
                <div className="flex flex-col md:flex-row gap-4">
                  <div className="flex-1">
                    <div className="relative">
                      <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                      <Input
                        placeholder="Search by student ID..."
                        value={filters.student_id || ''}
                        onChange={(e) => handleFilterChange('student_id', e.target.value)}
                        className="pl-10"
                      />
                    </div>
                  </div>

                  <Select
                    value={filters.status || 'all'}
                    onValueChange={(value) => handleFilterChange('status', value)}
                  >
                    <SelectTrigger className="w-full md:w-40">
                      <SelectValue placeholder="Status" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Status</SelectItem>
                      <SelectItem value="draft">Draft</SelectItem>
                      <SelectItem value="submitted">Submitted</SelectItem>
                    </SelectContent>
                  </Select>

                  <Select
                    value={filters.week_number?.toString() || 'all'}
                    onValueChange={(value) => handleFilterChange('week_number', value)}
                  >
                    <SelectTrigger className="w-full md:w-32">
                      <SelectValue placeholder="Week" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Weeks</SelectItem>
                      {Array.from({ length: 10 }, (_, i) => i + 1).map(week => (
                        <SelectItem key={week} value={week.toString()}>
                          Week {week}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </CardContent>
            </Card>

            {/* Tabs for Daily Logs and Weekly Reports */}
            <Tabs value={activeTab} onValueChange={setActiveTab}>
              <TabsList className="grid w-full grid-cols-2">
                <TabsTrigger value="daily-logs" className="flex items-center gap-2">
                  <BookOpen className="h-4 w-4" />
                  Daily Logs
                </TabsTrigger>
                <TabsTrigger value="weekly-reports" className="flex items-center gap-2">
                  <FileText className="h-4 w-4" />
                  Weekly Reports
                </TabsTrigger>
              </TabsList>

              <TabsContent value="daily-logs" className="space-y-4">
                <Card>
                  <CardHeader>
                    <CardTitle>Daily Log Entries</CardTitle>
                  </CardHeader>
                  <CardContent>
                    {loading ? (
                      <div className="flex items-center justify-center h-32">
                        <div className="text-center">
                          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
                          <p className="text-muted-foreground">Loading daily logs...</p>
                        </div>
                      </div>
                    ) : dailyLogs.length === 0 ? (
                      <div className="text-center py-8">
                        <BookOpen className="h-12 w-12 mx-auto mb-4 text-muted-foreground opacity-50" />
                        <h3 className="text-lg font-medium mb-2">No daily logs found</h3>
                        <p className="text-muted-foreground">No daily log entries match your current filters</p>
                      </div>
                    ) : (
                      <Table>
                        <TableHeader>
                          <TableRow>
                            <TableHead>Student</TableHead>
                            <TableHead>Week</TableHead>
                            <TableHead>Day</TableHead>
                            <TableHead>Title</TableHead>
                            <TableHead>Hours</TableHead>
                            <TableHead>Status</TableHead>
                            <TableHead>Date</TableHead>
                            <TableHead>Actions</TableHead>
                          </TableRow>
                        </TableHeader>
                        <TableBody>
                          {dailyLogs.map((log) => (
                            <TableRow key={log.id}>
                              <TableCell>
                                <div>
                                  <div className="font-medium">{log.student_name}</div>
                                  <div className="text-sm text-muted-foreground">{log.email}</div>
                                </div>
                              </TableCell>
                              <TableCell>
                                <Badge variant="outline">Week {log.week_number}</Badge>
                              </TableCell>
                              <TableCell>
                                <Badge variant="secondary">{log.day_of_week}</Badge>
                              </TableCell>
                              <TableCell>
                                <div className="max-w-[200px] truncate">{log.title}</div>
                              </TableCell>
                              <TableCell>
                                <div className="flex items-center gap-1">
                                  <Clock className="h-3 w-3" />
                                  {log.hours_worked}h
                                </div>
                              </TableCell>
                              <TableCell>
                                <Badge variant={log.is_submitted ? "default" : "secondary"}>
                                  {log.is_submitted ? "Submitted" : "Draft"}
                                </Badge>
                              </TableCell>
                              <TableCell>{formatDate(log.date)}</TableCell>
                              <TableCell>
                                <Button variant="ghost" size="sm">
                                  <Eye className="h-4 w-4" />
                                </Button>
                              </TableCell>
                            </TableRow>
                          ))}
                        </TableBody>
                      </Table>
                    )}
                  </CardContent>
                </Card>
              </TabsContent>

              <TabsContent value="weekly-reports" className="space-y-4">
                <Card>
                  <CardHeader>
                    <CardTitle>Weekly Reports</CardTitle>
                  </CardHeader>
                  <CardContent>
                    {loading ? (
                      <div className="flex items-center justify-center h-32">
                        <div className="text-center">
                          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
                          <p className="text-muted-foreground">Loading weekly reports...</p>
                        </div>
                      </div>
                    ) : weeklyReports.length === 0 ? (
                      <div className="text-center py-8">
                        <FileText className="h-12 w-12 mx-auto mb-4 text-muted-foreground opacity-50" />
                        <h3 className="text-lg font-medium mb-2">No weekly reports found</h3>
                        <p className="text-muted-foreground">No weekly reports match your current filters</p>
                      </div>
                    ) : (
                      <Table>
                        <TableHeader>
                          <TableRow>
                            <TableHead>Student</TableHead>
                            <TableHead>Week</TableHead>
                            <TableHead>Summary</TableHead>
                            <TableHead>Status</TableHead>
                            <TableHead>Submitted</TableHead>
                            <TableHead>Actions</TableHead>
                          </TableRow>
                        </TableHeader>
                        <TableBody>
                          {weeklyReports.map((report) => (
                            <TableRow key={report.id}>
                              <TableCell>
                                <div>
                                  <div className="font-medium">{report.student_name}</div>
                                  <div className="text-sm text-muted-foreground">{report.email}</div>
                                </div>
                              </TableCell>
                              <TableCell>
                                <Badge variant="outline">Week {report.week_number}</Badge>
                              </TableCell>
                              <TableCell>
                                <div className="max-w-[300px] truncate">{report.summary}</div>
                              </TableCell>
                              <TableCell>
                                <Badge variant={report.is_submitted ? "default" : "secondary"}>
                                  {report.is_submitted ? "Submitted" : "Draft"}
                                </Badge>
                              </TableCell>
                              <TableCell>
                                {report.submitted_at ? formatDate(report.submitted_at) : '-'}
                              </TableCell>
                              <TableCell>
                                <Button variant="ghost" size="sm">
                                  <Eye className="h-4 w-4" />
                                </Button>
                              </TableCell>
                            </TableRow>
                          ))}
                        </TableBody>
                      </Table>
                    )}
                  </CardContent>
                </Card>
              </TabsContent>
            </Tabs>
          </div>
        </div>
      </div>
      </div>
  )
}
