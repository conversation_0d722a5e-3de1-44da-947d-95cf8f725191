import axios from "axios";
import { toast } from "sonner";
import { Arrivalnote } from "../types/arrival_note";


export class ArrivalNote {
    public static api_url = process.env.NEXT_PUBLIC_API_URL;

    public static async init () {
        try {
            const token = localStorage.getItem('iptms_token');
            const response = await axios.get(`${this.api_url}/other/checkup`, {
              headers: {
                'Authorization': token
              }
            });
    
            if (response.status === 200) {
                return response.data.data;
            }
        } catch (error: any) {
            throw error.response.data;
        }
    }

  public static async index() {
    try {
      const response = await axios.get(`${this.api_url}/other/getArrivalNotes`);

      if (response.status === 200) {
        return response.data;
      }
    } catch (error: any) {
      throw error.response.data;
    }
  }
  
    public static async show(id: string) {
        try {
        const response = await axios.get(`${this.api_url}/other/getArrivalNote/${id}`);
    
        if (response.status === 200) {
            return response.data;
        }
        } catch (error: any) {
        throw error.response.data;
        }
    }
           
    public static async findByUserId(id: string) {
        try {
        const response = await axios.get(`${this.api_url}/other/getArrivalNoteByUserId/${id}`);
    
        if (response.status === 200) {
            return response.data.data;
        }
        } catch (error: any) {
        throw error.response.data;
        }
    }       

public static async create(data : Arrivalnote) {
    try {
        const response = await axios.post(`${this.api_url}/other/newArrivalNote`,data);

        if (response.status === 200) {
            toast.success(response.data.message);
            return response.data;
        }
    } catch (error: any) {
        throw error.response.data;
    }
}

public static async assign(student_id: string, supervisor_id: string, arrivalNote_id: string) {
    try {
        const response = await axios.post(`${this.api_url}/other/arrivalNote/assign/${student_id}`, {
            supervisor_id: supervisor_id,
            arrivalNote_id: arrivalNote_id
        });

        if (response.status === 200) {
            toast.success(response.data.message);
            return response.data;
        }
    } catch (error: any) {
        throw error.response.data;
    }
  }
}