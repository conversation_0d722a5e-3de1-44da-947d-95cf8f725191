"use client"

import { useState, useCallback } from "react"
import { useDropzone } from "react-dropzone"
import { Button } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Progress } from "@/components/ui/progress"
import { FileText, Upload, X, Eye, Download } from "lucide-react"
import { FileUpload } from "../types/logbook"
import { toast } from "sonner"

interface FileUploadProps {
  onFileSelect: (file: FileUpload) => void
  onFileRemove: () => void
  currentFile?: FileUpload
  maxSize?: number // in MB
  acceptedTypes?: string[]
}

export function FileUploadComponent({
  onFileSelect,
  onFileRemove,
  currentFile,
  maxSize = 10,
  acceptedTypes = ['.pdf', '.doc', '.docx']
}: FileUploadProps) {
  const [uploading, setUploading] = useState(false)

  const onDrop = useCallback(async (acceptedFiles: File[]) => {
    const file = acceptedFiles[0]
    if (!file) return

    // Validate file size
    if (file.size > maxSize * 1024 * 1024) {
      toast.error(`File size must be less than ${maxSize}MB`)
      return
    }

    // Validate file type
    const fileExtension = '.' + file.name.split('.').pop()?.toLowerCase()
    if (!acceptedTypes.includes(fileExtension)) {
      toast.error(`Only ${acceptedTypes.join(', ')} files are allowed`)
      return
    }

    setUploading(true)

    try {
      // Convert file to base64 for storage
      const reader = new FileReader()

      reader.onload = async (e) => {
        const base64String = e.target?.result as string

        // Create file preview
        let preview: string | undefined
        if (file.type === 'application/pdf') {
          preview = base64String // Use base64 for PDF preview
        }

        const fileUpload: FileUpload = {
          file,
          preview: base64String, // Store base64 as preview/URL
          progress: 0
        }

        // Simulate upload progress
        for (let i = 0; i <= 100; i += 10) {
          await new Promise(resolve => setTimeout(resolve, 50))
          fileUpload.progress = i
          onFileSelect({ ...fileUpload })
        }

        toast.success("File uploaded successfully")
        setUploading(false)
      }

      reader.onerror = () => {
        toast.error("Failed to process file")
        setUploading(false)
      }

      // Read file as base64
      reader.readAsDataURL(file)
    } catch (error) {
      toast.error("Failed to upload file")
      console.error("Upload error:", error)
      setUploading(false)
    }
  }, [maxSize, acceptedTypes, onFileSelect])

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      'application/pdf': ['.pdf'],
      'application/msword': ['.doc'],
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document': ['.docx']
    },
    maxFiles: 1,
    disabled: uploading
  })

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes'
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  const handlePreview = () => {
    if (currentFile?.preview) {
      window.open(currentFile.preview, '_blank')
    }
  }

  const handleDownload = () => {
    if (currentFile?.file) {
      const url = URL.createObjectURL(currentFile.file)
      const a = document.createElement('a')
      a.href = url
      a.download = currentFile.file.name
      document.body.appendChild(a)
      a.click()
      document.body.removeChild(a)
      URL.revokeObjectURL(url)
    }
  }

  if (currentFile) {
    return (
      <Card className="w-full">
        <CardContent className="p-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <FileText className="h-8 w-8 text-blue-500" />
              <div>
                <p className="font-medium text-sm">{currentFile.file.name}</p>
                <p className="text-xs text-muted-foreground">
                  {formatFileSize(currentFile.file.size)}
                </p>
              </div>
            </div>
            
            <div className="flex items-center space-x-2">
              {currentFile.file.type === 'application/pdf' && (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handlePreview}
                  className="h-8 w-8 p-0"
                >
                  <Eye className="h-4 w-4" />
                </Button>
              )}
              
              <Button
                variant="outline"
                size="sm"
                onClick={handleDownload}
                className="h-8 w-8 p-0"
              >
                <Download className="h-4 w-4" />
              </Button>
              
              <Button
                variant="outline"
                size="sm"
                onClick={onFileRemove}
                className="h-8 w-8 p-0 text-destructive hover:text-destructive"
              >
                <X className="h-4 w-4" />
              </Button>
            </div>
          </div>
          
          {currentFile.progress !== undefined && currentFile.progress < 100 && (
            <div className="mt-3">
              <Progress value={currentFile.progress} className="h-2" />
              <p className="text-xs text-muted-foreground mt-1">
                Uploading... {currentFile.progress}%
              </p>
            </div>
          )}
        </CardContent>
      </Card>
    )
  }

  return (
    <div
      {...getRootProps()}
      className={`
        border-2 border-dashed rounded-lg p-8 text-center cursor-pointer transition-colors
        ${isDragActive ? 'border-primary bg-primary/5' : 'border-muted-foreground/25'}
        ${uploading ? 'opacity-50 cursor-not-allowed' : 'hover:border-primary hover:bg-primary/5'}
      `}
    >
      <input {...getInputProps()} />
      
      <Upload className="h-12 w-12 mx-auto mb-4 text-muted-foreground" />
      
      {isDragActive ? (
        <p className="text-lg font-medium">Drop the file here...</p>
      ) : (
        <div>
          <p className="text-lg font-medium mb-2">
            {uploading ? 'Uploading...' : 'Drop your file here, or click to browse'}
          </p>
          <p className="text-sm text-muted-foreground">
            Supports PDF, DOC, DOCX files up to {maxSize}MB
          </p>
        </div>
      )}
    </div>
  )
}
