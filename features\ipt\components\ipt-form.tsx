import { DottedSeparator } from '@/components/dotted-separator';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import React from 'react'
import { useForm } from 'react-hook-form';
import { useGlobalManager } from '@/hooks/use-context';
import { useCreateIPT } from '../api/use-create-ipt';
import { useIptFormModal } from '../hooks/use-ipt-form-modal';

interface IptFormProps { 
    onCancel?: () => void;
}

type FormData = {
    name: string;
};

const IptForm = ({onCancel} : IptFormProps) => {
    const id = false;
    const { user } = useGlobalManager();
    const {close} = useIptFormModal();
    const user_id = user?.id;

    const createIPT = useCreateIPT();

    const {
        register,
        handleSubmit,
        formState: { errors },
        watch
    } = useForm<FormData>();

    const onSubmit = (data: FormData) => {
        // console.log(data.name , user_id);
        createIPT.mutate({ name: data.name, user_id: user_id }, {
            onSuccess: () => {
                close();
            },
        });
    }

  return (
    <Card className="w-full h-full border-none shadow-none">
            <CardHeader className="flex p-7">
                <CardTitle className="text-xl font-bold">
                    {id ? "Edit IPT" : "Create IPT"}
                </CardTitle>
            </CardHeader>
            <div className="px-7">
                <DottedSeparator />
            </div>
            <CardContent className="p-7">
            
                    <form onSubmit={handleSubmit(onSubmit)}>
                        <div className="flex flex-col gap-y-4">
                        <div className="grid gap-2">
                            <Label htmlFor="name">Name</Label>
                            <Input
                                id="name"
                                type="text"
                                autoFocus
                                placeholder="IPT 24/25"
                                {...register("name", { required: "IPT name is required" })}
                            />
                            <p className='text-slate-600 text-sm'>Start with "IPT" followed by the year of study like "24/25"</p>
                            {errors.name && (
                            <p className="text-sm text-red-500">{errors.name.message}</p>
                            )}
                        </div>
                        </div>
                        <DottedSeparator className="py-7" />
                        <div className="flex items-center justify-between">
                            <Button
                                type="button"
                                variant="secondary"
                                onClick={onCancel}
                                disabled={createIPT.isPending}
                            >
                                Cancel
                            </Button>
                            <Button 
                            disabled={createIPT.isPending || false}
                            >
                                {id ? "Update IPT" : "Create IPT"}
                            </Button>
                        </div>
                    </form>
            </CardContent>
        </Card>
  )
}

export default IptForm
