
import { DottedSeparator } from '@/components/dotted-separator';
import { ArrivalNote } from '@/features/arrrival_note/server/ArrivalNote';
import React from 'react'

export default async function page({params}: {params: {slug: string}}) {

  const slug = (await params).slug;

  const response = await ArrivalNote.show(slug);

  console.log(response)

    if (!response.success) {
        return (
        <div className="flex flex-1 items-center justify-center">
            <p className="text-muted-foreground">No arrival note found.</p>
        </div>
        )
    }

    const arrivalNote = response.data[0];

  return (
    <div className="flex flex-1 flex-col">
          <div className="@container/main flex flex-1 flex-col gap-2">
            <div className="flex flex-col gap-4 py-4 md:gap-6 md:py-6">
              <div className="flex flex-col gap-4 px-4 lg:px-6">
                <div className="flex flex-col gap-2">
                  <h1 className="text-2xl font-bold">Arrival Note</h1>
                  <p className="text-muted-foreground">
                    Here is where you can view student's arrival note.
                  </p>
                  <DottedSeparator />

                  <div className="mt-5">
                    <div className="px-4 lg:px-6">
                        <div className="rounded-lg border bg-card text-card-foreground shadow-sm p-6">
                          <div className="flex flex-col gap-4">
                          <div className="space-y-2">
                          <h3 className="text-xl font-semibold">{arrivalNote.first_name}{" "}{arrivalNote.middle_name}{" "}{arrivalNote.last_name}</h3>
                          <p className="text-sm text-muted-foreground">Submitted arrival Note</p>
                          </div>
                            <div className="grid gap-4 md:grid-cols-2">
                            <div className="space-y-2">
                            <h4 className="font-medium">Organization</h4>
                            <p className="text-sm text-muted-foreground">{arrivalNote.organization}</p>
                            </div>
                            <div className="space-y-2">
                            <h4 className="font-medium">Industrial Supervisor's name </h4>
                            <p className="text-sm text-muted-foreground">{arrivalNote.i_supervisor_name}</p>
                            </div>
                            <div className="space-y-2">
                            <h4 className="font-medium">Industrial Supervisor's Phone Number </h4>
                            <p className="text-sm text-muted-foreground">{arrivalNote.i_supervisor_phone}</p>
                            </div>
                            <div className="space-y-2">
                            <h4 className="font-medium">Region</h4>
                            <p className="text-sm text-muted-foreground">{arrivalNote.region}</p>
                            </div>
                            <div className="space-y-2">
                            <h4 className="font-medium">District</h4>
                            <p className="text-sm text-muted-foreground">{arrivalNote.district}</p>
                            </div>
                            <div className="space-y-2">
                            <h4 className="font-medium">Ward</h4>
                            <p className="text-sm text-muted-foreground">{arrivalNote.ward}</p>
                            </div>
                            <div className="space-y-2">
                            <h4 className="font-medium">Street</h4>
                            <p className="text-sm text-muted-foreground">{arrivalNote.street}</p>
                            </div>
                            <div className="space-y-2">
                            <h4 className="font-medium">House Number</h4>
                            <p className="text-sm text-muted-foreground">{arrivalNote.house_number}</p>
                            </div>
                            </div>
                        </div>
                        </div>
                        </div>
                  </div>
                </div>                
              </div>
              </div>
              </div>
          </div>
  )
}


