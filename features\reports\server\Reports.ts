import axios from "axios";
import { toast } from "sonner";
import { <PERSON>R<PERSON><PERSON>, <PERSON><PERSON><PERSON>, StudentReportSummary, AdminLogbookView, AdminReportFilters } from "../types/reports";

export class Reports {
  public static api_url = process.env.NEXT_PUBLIC_API_URL;

  // Final Report Methods
  public static async getFinalReport(student_id?: string) {
    try {
      const response = await axios.get(`${this.api_url}/reports/final-report`, {
        params: { student_id }
      });

      if (response.status === 200) {
        return response.data;
      }
    } catch (error: any) {
      toast.error(error.response?.data?.message || "Failed to fetch final report");
      throw error.response?.data;
    }
  }

  public static async saveFinalReport(data: FinalReport) {
    try {
      const response = await axios.post(`${this.api_url}/reports/final-report`, data);

      if (response.status === 200) {
        const message = data.is_submitted 
          ? "Final report submitted successfully" 
          : "Final report saved successfully";
        toast.success(response.data.message || message);
        return response.data;
      }
    } catch (error: any) {
      const message = data.is_submitted 
        ? "Failed to submit final report" 
        : "Failed to save final report";
      toast.error(error.response?.data?.message || message);
      throw error.response?.data;
    }
  }

  // Feedback Methods
  public static async getFeedback(student_id?: string) {
    try {
      const response = await axios.get(`${this.api_url}/reports/feedback`, {
        params: { student_id }
      });

      if (response.status === 200) {
        return response.data;
      }
    } catch (error: any) {
      toast.error(error.response?.data?.message || "Failed to fetch feedback");
      throw error.response?.data;
    }
  }

  public static async saveFeedback(data: Feedback) {
    try {
      // Get user info from localStorage if student_id is not provided
      if (!data.student_id) {
        const token = localStorage.getItem('iptms_token');
        if (token) {
          try {
            // Decode JWT to get user_id (simple decode, not verification)
            const payload = JSON.parse(atob(token.split('.')[1]));
            data.student_id = payload.user_id;
          } catch (e) {
            console.warn('Could not decode token for user_id');
          }
        }
      }

      const response = await axios.post(`${this.api_url}/reports/feedback`, data);

      if (response.status === 200) {
        const message = data.is_submitted
          ? "Feedback submitted successfully"
          : "Feedback saved successfully";
        toast.success(response.data.message || message);
        return response.data;
      }
    } catch (error: any) {
      const message = data.is_submitted
        ? "Failed to submit feedback"
        : "Failed to save feedback";
      toast.error(error.response?.data?.message || message);
      throw error.response?.data;
    }
  }

  public static async deleteFeedback(id: string) {
    try {
      const response = await axios.delete(`${this.api_url}/reports/feedback/${id}`);

      if (response.status === 200) {
        toast.success("Feedback deleted successfully");
        return response.data;
      }
    } catch (error: any) {
      toast.error(error.response?.data?.message || "Failed to delete feedback");
      throw error.response?.data;
    }
  }

  // File Upload Methods
  public static async uploadFile(file: File, type: 'final-report' | 'feedback', category?: string) {
    try {
      const formData = new FormData();
      formData.append('file', file);
      formData.append('type', type);
      if (category) formData.append('category', category);

      const response = await axios.post(`${this.api_url}/reports/upload`, formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });

      if (response.status === 200) {
        toast.success("File uploaded successfully");
        return response.data;
      }
    } catch (error: any) {
      toast.error(error.response?.data?.message || "Failed to upload file");
      throw error.response?.data;
    }
  }

  // Admin Methods
  public static async getStudentReportsSummary(filters?: AdminReportFilters) {
    try {
      const response = await axios.get(`${this.api_url}/admin/reports/summary`, {
        params: filters
      });

      if (response.status === 200) {
        return response.data;
      }
    } catch (error: any) {
      toast.error(error.response?.data?.message || "Failed to fetch reports summary");
      throw error.response?.data;
    }
  }

  public static async getStudentLogbooks(filters?: AdminReportFilters) {
    try {
      const response = await axios.get(`${this.api_url}/admin/reports/logbooks`, {
        params: filters
      });

      if (response.status === 200) {
        return response.data;
      }
    } catch (error: any) {
      toast.error(error.response?.data?.message || "Failed to fetch student logbooks");
      throw error.response?.data;
    }
  }

  public static async getAllFinalReports(filters?: AdminReportFilters) {
    try {
      const response = await axios.get(`${this.api_url}/admin/reports/final-reports`, {
        params: filters
      });

      if (response.status === 200) {
        return response.data;
      }
    } catch (error: any) {
      toast.error(error.response?.data?.message || "Failed to fetch final reports");
      throw error.response?.data;
    }
  }

  public static async getAllFeedback(filters?: AdminReportFilters) {
    try {
      const response = await axios.get(`${this.api_url}/admin/reports/feedback`, {
        params: filters
      });

      if (response.status === 200) {
        return response.data;
      }
    } catch (error: any) {
      toast.error(error.response?.data?.message || "Failed to fetch feedback");
      throw error.response?.data;
    }
  }

  public static async exportReports(type: 'logbooks' | 'final-reports' | 'feedback', format: 'csv' | 'excel' | 'pdf', filters?: AdminReportFilters) {
    try {
      const response = await axios.get(`${this.api_url}/admin/reports/export`, {
        params: { type, format, ...filters },
        responseType: 'blob'
      });

      if (response.status === 200) {
        // Create download link
        const url = window.URL.createObjectURL(new Blob([response.data]));
        const link = document.createElement('a');
        link.href = url;
        link.setAttribute('download', `${type}_export.${format}`);
        document.body.appendChild(link);
        link.click();
        link.remove();
        window.URL.revokeObjectURL(url);
        
        toast.success("Export completed successfully");
        return response.data;
      }
    } catch (error: any) {
      toast.error(error.response?.data?.message || "Failed to export reports");
      throw error.response?.data;
    }
  }
}
