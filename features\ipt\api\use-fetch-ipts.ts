import { useQuery } from "@tanstack/react-query";
import { toast } from "sonner";
import { Ipt } from "../server/Ipt";


export const useFetchIPTs = () => {

    const query = useQuery({
        queryKey: ["ipt"],
        queryFn: async () => {
            try {
                const res = await Ipt.index();
                if (res.success) {
                    return res.data;
                }
                return [];
            } catch (error: any) {
                toast.error(error.message);
                return [];
            }
        },
        // initialData: []
    });
    return query;
}
