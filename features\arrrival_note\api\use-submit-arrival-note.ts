import { useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";
import { ArrivalNote } from "../server/ArrivalNote";
import { Arrivalnote } from "../types/arrival_note";
import { useGlobalManager } from "@/hooks/use-context";

export const useSubmitArrivalNote = () => {
    const queryClient = useQueryClient();
    const { fetchArrivalNotesData } = useGlobalManager();

    const mutation = useMutation<any, Error, Arrivalnote>({ 
        mutationFn: async (data: Arrivalnote) => {
            // console.log("data", data);
            const res = await ArrivalNote.create(data);
            return res;
        },
        onSuccess: (data) => {
            fetchArrivalNotesData();
            queryClient.invalidateQueries({ queryKey: ["arrival_notes"] });
        },
        onError: (error) => {
            toast.error(error.message);
        },
    });

    return mutation;
};
