import { useMutation, useQueryClient } from "@tanstack/react-query"
import { toast } from "sonner";
import { Supervisor } from "../server/Supervisor";
import { useGlobalManager } from "@/hooks/use-context";


export const useSupervisorLogin = () => {
    const queryClient = useQueryClient();
      const { handleLogin } = useGlobalManager();
    
    const mutation = useMutation({
        mutationFn: async (data: { email: string; password: string }) => {
            const res = await Supervisor.login(data.email, data.password);
            return res;
        },
        onSuccess: (data) => {
            handleLogin(data);
            queryClient.invalidateQueries({ queryKey: ["supervisors"] });
    
        },
    
        onError: (error) => {
          toast.error(error.message);
        },
    });

    return mutation;
}