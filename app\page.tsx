"use client";

import { SectionCards } from "@/components/section-cards";
import { StudentDashboard } from "@/features/students/components/dashboard";
import { useSupervisorFormModal } from "@/features/supervisors/hooks/use-supervisor-form-modal";
import { useGlobalManager } from "@/hooks/use-context";
import { useEffect } from "react";
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import { User, User2, FileText, ClipboardCheck, BookOpen, PieChart } from "lucide-react";
import Link from "next/link";
import { Button } from "@/components/ui/button";
import { DottedSeparator } from "@/components/dotted-separator";

export default function Home() {

  const { user , needsPasswordChange} = useGlobalManager();
  const { changePasswordOpen } = useSupervisorFormModal();

  useEffect(() => {
    if(needsPasswordChange){
      changePasswordOpen();
    }
  },[needsPasswordChange])

  return (
    <div className="flex flex-1 flex-col">
      <div className="@container/main flex flex-1 flex-col gap-2">
        <div className="flex flex-col gap-4 py-4 md:gap-6 md:py-6">
          {user?.role == "admin" && (
            <>
              <div className="flex flex-col gap-4 px-4 lg:px-6">
                <div className="flex flex-col gap-2">
                  <h1 className="text-2xl font-bold">Welcome to IPTMS</h1>
                  <p className="text-muted-foreground">
                    Administrative dashboard for Industrial Practical Training Management
                  </p>
                  <DottedSeparator />
                </div>
              </div>
              
              {/* Admin Analytics Dashboard */}
              <div className="grid gap-4 px-4 lg:px-6 md:grid-cols-2 lg:grid-cols-4">
                {/* Students Card */}
                <Card>
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle className="text-sm font-medium">Total Students</CardTitle>
                    <User className="h-4 w-4 text-primary" />
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">247</div>
                    <p className="text-xs text-muted-foreground">
                      +12% from last semester
                    </p>
                    <Progress value={72} className="mt-2" />
                  </CardContent>
                </Card>

                {/* Supervisors Card */}
                <Card>
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle className="text-sm font-medium">Supervisors</CardTitle>
                    <User2 className="h-4 w-4 text-primary" />
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">38</div>
                    <p className="text-xs text-muted-foreground">
                      +3 new this semester
                    </p>
                    <Progress value={85} className="mt-2" />
                  </CardContent>
                </Card>

                {/* Arrival Notes Card */}
                <Card>
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle className="text-sm font-medium">Arrival Notes</CardTitle>
                    <ClipboardCheck className="h-4 w-4 text-primary" />
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">186</div>
                    <p className="text-xs text-muted-foreground">
                      75% submission rate
                    </p>
                    <Progress value={75} className="mt-2" />
                  </CardContent>
                </Card>

                {/* Programs Card */}
                <Card>
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle className="text-sm font-medium">Active Programs</CardTitle>
                    <BookOpen className="h-4 w-4 text-primary" />
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">12</div>
                    <p className="text-xs text-muted-foreground">
                      Across 4 faculties
                    </p>
                    <Progress value={92} className="mt-2" />
                  </CardContent>
                </Card>
              </div>

              <DottedSeparator className="mx-4 lg:mx-6" />

              {/* Recent Activities Section */}
              <div className="px-4 lg:px-6 grid gap-4 md:grid-cols-2">
                <Card className="col-span-1">
                  <CardHeader>
                    <CardTitle>Recent Submissions</CardTitle>
                    <CardDescription>Latest student activity in the system</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      {[1, 2, 3, 4].map((i) => (
                        <div key={i} className="flex items-center gap-4 border-b pb-3">
                          <div className="rounded-full bg-primary/10 p-2">
                            <FileText className="h-4 w-4 text-primary" />
                          </div>
                          <div className="flex-1">
                            <p className="text-sm font-medium">Student {i} submitted arrival note</p>
                            <p className="text-xs text-muted-foreground">{i * 2} hours ago</p>
                          </div>
                          <Link href="/admin/arrival_notes">
                            <Button variant="ghost" size="sm">View</Button>
                          </Link>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>

                <Card className="col-span-1">
                  <CardHeader>
                    <CardTitle>Placement Distribution</CardTitle>
                    <CardDescription>Students by region</CardDescription>
                  </CardHeader>
                  <CardContent className="h-[220px] flex items-center justify-center">
                    <div className="w-full h-full flex items-center justify-center">
                      <PieChart className="h-32 w-32 text-muted-foreground" />
                      <div className="ml-4 space-y-2">
                        <div className="flex items-center gap-2">
                          <div className="h-3 w-3 rounded-full bg-primary"></div>
                          <span className="text-sm">Dar es Salaam (42%)</span>
                        </div>
                        <div className="flex items-center gap-2">
                          <div className="h-3 w-3 rounded-full bg-blue-500"></div>
                          <span className="text-sm">Arusha (18%)</span>
                        </div>
                        <div className="flex items-center gap-2">
                          <div className="h-3 w-3 rounded-full bg-green-500"></div>
                          <span className="text-sm">Mwanza (15%)</span>
                        </div>
                        <div className="flex items-center gap-2">
                          <div className="h-3 w-3 rounded-full bg-yellow-500"></div>
                          <span className="text-sm">Others (25%)</span>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>

              <div className="px-4 lg:px-6 mt-4">
                <Card>
                  <CardHeader>
                    <CardTitle>Quick Actions</CardTitle>
                    <CardDescription>Frequently used administrative tasks</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="grid gap-4 md:grid-cols-3">
                      <Link href="/admin/students">
                        <Card className="hover:bg-accent cursor-pointer transition-colors">
                          <CardContent className="flex items-center p-6">
                            <User className="h-5 w-5 mr-3 text-primary" />
                            <div>
                              <h3 className="font-medium">Manage Students</h3>
                              <p className="text-sm text-muted-foreground">View and edit student records</p>
                            </div>
                          </CardContent>
                        </Card>
                      </Link>
                      
                      <Link href="/admin/supervisors">
                        <Card className="hover:bg-accent cursor-pointer transition-colors">
                          <CardContent className="flex items-center p-6">
                            <User2 className="h-5 w-5 mr-3 text-primary" />
                            <div>
                              <h3 className="font-medium">Manage Supervisors</h3>
                              <p className="text-sm text-muted-foreground">Assign and manage supervisors</p>
                            </div>
                          </CardContent>
                        </Card>
                      </Link>
                      
                      <Link href="/admin/arrival_notes">
                        <Card className="hover:bg-accent cursor-pointer transition-colors">
                          <CardContent className="flex items-center p-6">
                            <ClipboardCheck className="h-5 w-5 mr-3 text-primary" />
                            <div>
                              <h3 className="font-medium">Arrival Notes</h3>
                              <p className="text-sm text-muted-foreground">Review student submissions</p>
                            </div>
                          </CardContent>
                        </Card>
                      </Link>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </>
          )}
          {user?.role == "supervisor" && (
            <>
              <div className="flex flex-col gap-4 px-4 lg:px-6">
                <div className="flex flex-col gap-2">
                  <h1 className="text-2xl font-bold">Welcome to IPTMS</h1>
                  <p className="text-muted-foreground">
                    This is your dashboard. You can manage your IPT here.
                  </p>
                </div>
              </div>
              
              <StudentDashboard />
            </>
          )}
          {user?.role == "student" && (
            <>
              <div className="flex flex-col gap-4 px-4 lg:px-6">
                <div className="flex flex-col gap-2">
                  <h1 className="text-2xl font-bold">Welcome to IPTMS</h1>
                  <p className="text-muted-foreground">
                    This is your dashboard. You can manage your IPT here.
                  </p>
                </div>
              </div>
              <StudentDashboard />
            </>
          )}
        </div>
      </div>
    </div>
  );
}
