"use client";

import { SectionCards } from "@/components/section-cards";
import { StudentDashboard } from "@/features/students/components/dashboard";
import { AdminDashboard } from "@/features/admin/components/admin-dashboard";
import { useSupervisorFormModal } from "@/features/supervisors/hooks/use-supervisor-form-modal";
import { useGlobalManager } from "@/hooks/use-context";
import { useEffect } from "react";

export default function Home() {

  const { user , needsPasswordChange} = useGlobalManager();
  const { changePasswordOpen } = useSupervisorFormModal();

  useEffect(() => {
    if(needsPasswordChange){
      changePasswordOpen();
    }
  },[needsPasswordChange])

  return (
    <div className="flex flex-1 flex-col">
      <div className="@container/main flex flex-1 flex-col gap-2">
        <div className="flex flex-col gap-4 py-4 md:gap-6 md:py-6">
          {user?.role == "admin" && <AdminDashboard />}
          {user?.role == "supervisor" && (
            <>
              <div className="flex flex-col gap-4 px-4 lg:px-6">
                <div className="flex flex-col gap-2">
                  <h1 className="text-2xl font-bold">Welcome to IPTMS</h1>
                  <p className="text-muted-foreground">
                    This is your dashboard. You can manage your IPT here.
                  </p>
                </div>
              </div>
              
              <StudentDashboard />
            </>
          )}
          {user?.role == "student" && (
            <>
              <div className="flex flex-col gap-4 px-4 lg:px-6">
                <div className="flex flex-col gap-2">
                  <h1 className="text-2xl font-bold">Welcome to IPTMS</h1>
                  <p className="text-muted-foreground">
                    This is your dashboard. You can manage your IPT here.
                  </p>
                </div>
              </div>
              <StudentDashboard />
            </>
          )}
        </div>
      </div>
    </div>
  );
}
