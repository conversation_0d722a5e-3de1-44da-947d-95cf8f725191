import { useState, useEffect } from 'react';
import { Progress } from './ui/progress';

interface ProgressBarProps {
  loading: boolean;
}

const ProgressBar = ({ loading }: ProgressBarProps) => {
  const [progress, setProgress] = useState(0);

  const [isDarkMode, setIsDarkMode] = useState(false);

  useEffect(() => {
    setIsDarkMode(document.documentElement.classList.contains('dark'));
    if (loading) {
      const interval = setInterval(() => {
        setProgress((prevProgress) => {
          if (prevProgress >= 100) {
            clearInterval(interval);
            return 100;
          }
          return prevProgress + 1;
        });
      }, 1);

      return () => clearInterval(interval);
    }
  }, [loading]);

  return (
    <div className="flex min-h-screen flex-col items-center justify-center gap-6 bg-black p-6 md:p-10">
      <div className="flex w-full max-w-sm flex-col gap-6">
        <Progress
          value={progress}
          className={`w-[60%] ${isDarkMode ? 'bg-white' : 'bg-black'}`}
        />
      </div>
    </div>
  );
};

export default ProgressBar;
