import { Button } from "@/components/ui/button"
import { Checkbox } from "@/components/ui/checkbox"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuLabel, DropdownMenuSeparator, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import { ColumnDef } from "@tanstack/react-table"
import { ArrowUpDown, Delete, Edit, Eye, MoreHorizontal } from "lucide-react"
import { dateDiff, formatDate } from "@/lib/utils"
import Link from "next/link"
import { supervisor } from "../auth/types/auth"
import { SupervisorTableActions } from "./components/supervisor-table-actions"

export const SupervisorColumns: ColumnDef<supervisor>[] = [
  {
    id: "select",
    header: ({ table }) => (
      <Checkbox
        checked={
          table.getIsAllPageRowsSelected() ||
          (table.getIsSomePageRowsSelected() && "indeterminate")
        }
        onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
        aria-label="Select all"
      />
    ),
    cell: ({ row }) => (
      <Checkbox
        checked={row.getIsSelected()}
        onCheckedChange={(value) => row.toggleSelected(!!value)}
        aria-label="Select row"
      />
    ),
    enableSorting: false,
    enableHiding: false,
  },
  {
    accessorKey: "first_name",
    header: "Name",
    cell: ({ row }) => {
        const supervisor = row.original;
        
        return <div className="uppercase">{supervisor?.first_name+ ' ' + supervisor?.last_name}</div>;
    },
  },
  {
    accessorKey: "phone",
    header: "Phone",
    cell: ({ row }) => {
        const supervisor = row.original;
        
        return <div className="uppercase">{supervisor?.phone}</div>;
    },
  },
  {
    accessorKey: "email",
    header: "Email",
    cell: ({ row }) => {
        const supervisor = row.original;
        
        return <div className="">{supervisor?.email}</div>;
    },
  },
  
  {
    accessorKey: "created_at",
    header: ({ column }) => {
      return (
        <Button
          variant="ghost"
          onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
        >
          Created
          <ArrowUpDown />
        </Button>
      )
    },
    cell: ({ row }) => <div className="">{row.original.created_at ? formatDate(row.original.created_at ?? '') : 'NaN'}</div>,
  },
  {
    accessorKey: "updated_at",
    header: ({ column }) => {
      return (
        <Button
          variant="ghost"
          onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
        >
          Updated
          <ArrowUpDown />
        </Button>
      )
    },
    cell: ({ row }) => <div className="">{row.original.updated_at ? dateDiff(row.original.updated_at ?? '') : "NaN"}</div>,
  },
  {
    id: "actions",
    enableHiding: false,
    cell: ({ row }) => {
      const supervisor = row.original

      return (
        <SupervisorTableActions supervisor={supervisor} />
      )
    },
  },
]