"use client"

import { useEffect, useState } from "react"
import { useGlobalManager } from "@/hooks/use-context"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Progress } from "@/components/ui/progress"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { DottedSeparator } from "@/components/dotted-separator"
import { useArrivalNoteFormModal } from "@/features/arrrival_note/hooks/use-arrival-note-form-modal"
import { useRetrieveArrivalNote } from "@/features/arrrival_note/api/use-retrieve-arrival-note"
import { IPTLogbooks } from "@/features/logbook/server/IPTLogbooks"
import { FinalReports } from "@/features/reports/server/FinalReports"
import { CalendarDays, ClipboardCheck, FileText, User } from "lucide-react"
import Link from "next/link"

export const StudentDashboard = () => {
  const { user, hasSubmitArrivalNote } = useGlobalManager()
  const arrivalNote = useArrivalNoteFormModal()
  const { data: arrivalNoteData } = useRetrieveArrivalNote(user?.id)
  const [logbookProgress, setLogbookProgress] = useState(0)
  const [finalReportStatus, setFinalReportStatus] = useState<'not_started' | 'draft' | 'submitted'>('not_started')
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    loadDashboardData()
  }, [user?.id])

  const loadDashboardData = async () => {
    if (!user?.id) return

    try {
      setLoading(true)

      // Load logbook progress
      const progressResult = await IPTLogbooks.getStudentProgress()
      if (progressResult?.data) {
        setLogbookProgress(progressResult.data.overall_progress || 0)
      }

      // Load final report status
      try {
        const finalReportResult = await FinalReports.getMyFinalReport()
        if (finalReportResult?.data) {
          setFinalReportStatus(finalReportResult.data.is_submitted ? 'submitted' : 'draft')
        }
      } catch (error) {
        // No final report exists yet
        setFinalReportStatus('not_started')
      }

    } catch (error) {
      console.error('Failed to load dashboard data:', error)
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="flex flex-col gap-6">
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        {/* Arrival Note Status Card */}
        <Card className={hasSubmitArrivalNote ? "border-green-200" : "border-amber-200"}>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Arrival Note</CardTitle>
            <ClipboardCheck className={hasSubmitArrivalNote ? "h-4 w-4 text-green-500" : "h-4 w-4 text-amber-500"} />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {hasSubmitArrivalNote ? "Submitted" : "Pending"}
            </div>
            <p className="text-xs text-muted-foreground">
              {hasSubmitArrivalNote 
                ? "Your arrival note has been submitted successfully" 
                : "You need to submit your arrival note"}
            </p>
            {!hasSubmitArrivalNote && (
              <Button 
                variant="outline" 
                size="sm" 
                className="mt-2" 
                onClick={arrivalNote.open}
              >
                Submit Now
              </Button>
            )}
          </CardContent>
        </Card>

        {/* Supervisor Card */}
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Supervisor</CardTitle>
            <User className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {arrivalNoteData?.i_supervisor_name || "Not Assigned"}
            </div>
            <p className="text-xs text-muted-foreground">
              {arrivalNoteData?.i_supervisor_phone || "No contact information"}
            </p>
          </CardContent>
        </Card>

        {/* Logbook Progress Card */}
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Logbook Progress</CardTitle>
            <CalendarDays className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="flex items-center justify-between">
              <div className="text-2xl font-bold">
                {loading ? "..." : `${logbookProgress}%`}
              </div>
              <Link href="/students/logbook">
                <Button variant="ghost" size="sm">View</Button>
              </Link>
            </div>
            <Progress value={logbookProgress} className="mt-2" />
            <p className="text-xs text-muted-foreground mt-2">
              {loading ? "Loading..." : `${10 - Math.floor(logbookProgress / 10)} weeks remaining`}
            </p>
          </CardContent>
        </Card>

        {/* Final Report Card */}
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Final Report</CardTitle>
            <FileText className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {loading ? "..." :
                finalReportStatus === 'not_started' ? 'Not Started' :
                finalReportStatus === 'draft' ? 'Draft' : 'Submitted'
              }
            </div>
            <p className="text-xs text-muted-foreground">
              {finalReportStatus === 'submitted' ? 'Report submitted successfully' : 'Due at end of internship'}
            </p>
            <Link href="/students/final-report">
              <Button variant="outline" size="sm" className="mt-2">
                {finalReportStatus === 'not_started' ? 'Start Report' :
                 finalReportStatus === 'draft' ? 'Continue Report' : 'View Report'}
              </Button>
            </Link>
          </CardContent>
        </Card>
      </div>

      <DottedSeparator />

      {/* Recent Activities Section */}
      <div>
        <h2 className="text-xl font-semibold mb-4">Recent Activities</h2>
        <div className="space-y-4">
          {hasSubmitArrivalNote ? (
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">Organization Details</CardTitle>
                <CardDescription>Your current placement information</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid gap-4 md:grid-cols-2">
                  <div className="space-y-2">
                    <h4 className="font-medium">Organization</h4>
                    <p className="text-sm text-muted-foreground">{arrivalNoteData?.organization || "N/A"}</p>
                  </div>
                  <div className="space-y-2">
                    <h4 className="font-medium">Location</h4>
                    <p className="text-sm text-muted-foreground">
                      {arrivalNoteData?.region}, {arrivalNoteData?.district}
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
          ) : (
            <Card>
              <CardContent className="pt-6">
                <div className="flex flex-col items-center justify-center text-center p-6">
                  <ClipboardCheck className="h-10 w-10 text-muted-foreground mb-4" />
                  <h3 className="text-lg font-medium">Submit Your Arrival Note</h3>
                  <p className="text-sm text-muted-foreground mt-2 mb-4">
                    You need to submit your arrival note to start tracking your IPT progress
                  </p>
                  <Button onClick={arrivalNote.open}>Submit Arrival Note</Button>
                </div>
              </CardContent>
            </Card>
          )}
        </div>
      </div>

      {/* Quick Links */}
      <div>
        <h2 className="text-xl font-semibold mb-4">Quick Links</h2>
        <div className="grid gap-4 md:grid-cols-3">
          <Link href="/students/logbook">
            <Card className="hover:bg-accent cursor-pointer transition-colors">
              <CardContent className="flex items-center p-6">
                <CalendarDays className="h-5 w-5 mr-3 text-primary" />
                <div>
                  <h3 className="font-medium">Logbook</h3>
                  <p className="text-sm text-muted-foreground">Update your daily activities</p>
                </div>
              </CardContent>
            </Card>
          </Link>
          
          <Link href="/students/f_report">
            <Card className="hover:bg-accent cursor-pointer transition-colors">
              <CardContent className="flex items-center p-6">
                <FileText className="h-5 w-5 mr-3 text-primary" />
                <div>
                  <h3 className="font-medium">Final Report</h3>
                  <p className="text-sm text-muted-foreground">Prepare your final submission</p>
                </div>
              </CardContent>
            </Card>
          </Link>
          
          <Link href="/profile">
            <Card className="hover:bg-accent cursor-pointer transition-colors">
              <CardContent className="flex items-center p-6">
                <User className="h-5 w-5 mr-3 text-primary" />
                <div>
                  <h3 className="font-medium">Profile</h3>
                  <p className="text-sm text-muted-foreground">Update your personal details</p>
                </div>
              </CardContent>
            </Card>
          </Link>
        </div>
      </div>
    </div>
  )
}

export default function DashboardPage() {
  const { user } = useGlobalManager()

  return (
    <div className="flex flex-1 flex-col">
      <div className="@container/main flex flex-1 flex-col gap-2">
        <div className="flex flex-col gap-4 py-4 md:gap-6 md:py-6">
          {user?.role === "admin" && (
            <>
              <div className="flex flex-col gap-4 px-4 lg:px-6">
                <div className="flex flex-col gap-2">
                  <h1 className="text-2xl font-bold">Welcome to IPTMS</h1>
                  <p className="text-muted-foreground">
                    This is your dashboard. You can manage your IPT here.
                  </p>
                </div>
              </div>
              {/* Admin dashboard content */}
            </>
          )}
          
          {user?.role === "supervisor" && (
            <>
              <div className="flex flex-col gap-4 px-4 lg:px-6">
                <div className="flex flex-col gap-2">
                  <h1 className="text-2xl font-bold">Welcome to IPTMS</h1>
                  <p className="text-muted-foreground">
                    This is your dashboard. You can manage your IPT here.
                  </p>
                </div>
              </div>
              {/* Supervisor dashboard content */}
            </>
          )}
          
          {user?.role === "student" && (
            <>
              <div className="flex flex-col gap-4 px-4 lg:px-6">
                <div className="flex flex-col gap-2">
                  <h1 className="text-2xl font-bold">Welcome to IPTMS</h1>
                  <p className="text-muted-foreground">
                    This is your dashboard. You can manage your IPT here.
                  </p>
                  <DottedSeparator />
                </div>
                <StudentDashboard />
              </div>
            </>
          )}
        </div>
      </div>
    </div>
  )
}