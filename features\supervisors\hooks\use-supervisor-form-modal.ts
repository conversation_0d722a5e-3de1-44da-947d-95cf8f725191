import { parseAsBoolean, parseAsString, useQueryState } from "nuqs";
import { useEffect } from "react";

export const useSupervisorFormModal = () => {
  const [isOpen, setIsOpen] = useQueryState(
    "supervisor-form",
    parseAsBoolean.withDefault(false).withOptions({ clearOnDefault: true })
  );
  const [isChangePasswordOpen, setIsChangePasswordOpen] = useQueryState(
    "supervisor-change-password-form",
    parseAsBoolean.withDefault(false).withOptions({ clearOnDefault: true })
  );

const [id, setId] = useQueryState(
  "supervisor-form-id", 
  parseAsString.withDefault("").withOptions({ clearOnDefault: true }));

const open = () => setIsOpen(true);
const changePasswordOpen = () => setIsChangePasswordOpen(true);
const close = () => setIsOpen(false);

const edit = (id: string) => {
  setId(id);
  setIsOpen(true);
}

useEffect(() => {
  if (!isOpen) {
    setId("");
  }
}, [isOpen, setId]);

return {
  id,
  isOpen,
  open,
  close,
  setIsOpen,
  edit,
  changePasswordOpen,
  isChangePasswordOpen,
  setIsChangePasswordOpen
};
};
