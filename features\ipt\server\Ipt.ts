import axios from "axios";
import { toast } from "sonner";

export class Ipt {
  public static api_url = process.env.NEXT_PUBLIC_API_URL;

  public static async index() {
    try { 
      const response = await axios.get(`${this.api_url}/other/getIPTs`);

      if (response.status === 200) {
        return response.data;
      }
    } catch (error: any) {
      throw error.response.data;
    }
  }
  
  public static async create(name: string , user_id: string) {

    try {
      const response = await axios.post(`${this.api_url}/other/newIPT`, {
        name , user_id
      });

      if (response.status === 200) {
        toast.success(response.data.message);

        return response.data; 
      }

    } catch (error: any) {
      throw error.response.data; 
    }
  }

}
