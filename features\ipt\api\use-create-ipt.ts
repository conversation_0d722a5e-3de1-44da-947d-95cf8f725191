import { useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";
import { IptData, useGlobalManager } from "@/hooks/use-context";
import { Ipt } from "../server/Ipt";

export const useCreateIPT = () => {
  const queryClient = useQueryClient();
  const { changeIptId } = useGlobalManager();

  const mutation = useMutation<IptData, Error, { name: string , user_id: string }>({ 
    mutationFn: async ({ name , user_id }: { name: string; user_id: string }) => {
      const res = await Ipt.create(name , user_id);
      return res;
    },
    onSuccess: (data) => {

        changeIptId(data);

        queryClient.invalidateQueries({ queryKey: ["ipt"] });

    },

    onError: (error) => {
      toast.error(error.message);
    },
  });

  return mutation;
};
