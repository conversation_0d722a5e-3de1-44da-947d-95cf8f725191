import { useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";
import { Auth } from "../server/Auth";
import { useGlobalManager } from "@/hooks/use-context";

export const useUpdateProfile = () => {
  const queryClient = useQueryClient();
  const { setUser } = useGlobalManager();

  const mutation = useMutation<
    any, 
    Error, 
    { email: string; fname: string; lname: string; phone: string }
  >({ 
    mutationFn: async (userData: { 
      email: string; 
      fname: string; 
      lname: string; 
      phone: string 
    }) => {
      const res = await Auth.updateProfile(userData);
      return res;
    },
    onSuccess: (data) => {
      if (data?.data) {
        setUser(data.data);
      }
      
      queryClient.invalidateQueries({ queryKey: ["user"] });
    },
    onError: (error) => {
      toast.error(error.message);
    },
  });

  return mutation;
};