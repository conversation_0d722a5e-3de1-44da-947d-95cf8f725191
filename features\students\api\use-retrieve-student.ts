import { useQuery } from "@tanstack/react-query";
import { Student } from "../server/Student";

export const useRetrieveStudent = (id: string) => {
    const query = useQuery({
        enabled: !!id,
        queryKey: ["students", id],
        queryFn: async () => {
          const res = await Student.show(id);
    
          // if (!res.ok) {
          //   return null;
          // }
          // console.log(res)
          return res[0];
        },
    });
    
    return query;
}