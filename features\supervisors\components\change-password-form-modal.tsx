"use client"

import { ResponsiveModal } from "@/components/responsive-modal";
import { useSupervisorFormModal } from "../hooks/use-supervisor-form-modal";
import { ChangePasswordForm } from "./change-password-form";


export const ChangePasswordFormModal = () => {
    const { isChangePasswordOpen, setIsChangePasswordOpen } = useSupervisorFormModal();

    return (
        <ResponsiveModal open={isChangePasswordOpen} onOpenChange={setIsChangePasswordOpen}>
            <ChangePasswordForm />
        </ResponsiveModal>
    )
}