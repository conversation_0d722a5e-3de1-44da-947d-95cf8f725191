import { But<PERSON> } from "@/components/ui/button"
import { Checkbox } from "@/components/ui/checkbox"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuLabel, DropdownMenuSeparator, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import { ColumnDef } from "@tanstack/react-table"
import { ArrowUpDown, MoreHorizontal } from "lucide-react"
import { Arrivalnote } from "./types/arrival_note"
import { dateDiff, formatDate } from "@/lib/utils"
import Link from "next/link"
import { badgeVariants } from "@/components/ui/badge"
import { Combobox } from "@/components/combobox"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { useEffect, useState } from "react"
import { useFetchSupervisor } from "../supervisors/api/use-fetch-supervisors"
import { useAssignSupervisor } from "./api/use-assign-supervisor"

export const ArrivalNoteColumns: ColumnDef<Arrivalnote>[] = [
  {
    id: "select",
    header: ({ table }) => (
      <Checkbox
        checked={
          table.getIsAllPageRowsSelected() ||
          (table.getIsSomePageRowsSelected() && "indeterminate")
        }
        onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
        aria-label="Select all"
      />
    ),
    cell: ({ row }) => (
      <Checkbox
        checked={row.getIsSelected()}
        onCheckedChange={(value) => row.toggleSelected(!!value)}
        aria-label="Select row"
      />
    ),
    enableSorting: false,
    enableHiding: false,
  },
  {
    accessorKey: "student",
    header: "Student Name",
    cell: ({ row }) => {
        const student = row.original;
        
        return <div className="uppercase">{student?.first_name + ' ' + student?.middle_name + ' ' + student?.last_name}</div>;
    },
  },
  {
    accessorKey: "reg_no",
    header: ({ column }) => {
      return (
        <Button
          variant="ghost"
          onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
        >
          Registration
          <ArrowUpDown />
        </Button>
      )
    },
    cell: ({ row }) => <div className="lowercase">{row.original?.reg_no}</div>,
  },
  {
    accessorKey: "class",
    header: ({ column }) => {
      return (
        <Button
          variant="ghost"
          onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
        >
          Class
          <ArrowUpDown />
        </Button>
      )
    },
    cell: ({ row }) => <div className="uppercase">{row.original?.class}</div>,
  },
  {
    accessorKey: "is_assigned",
    header: ({ column }) => {
      return (
        <Button
          variant="ghost"
          onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
        >
          Is Assigned
          <ArrowUpDown />
        </Button>
      )
    },
    cell: ({ row }) => <div className="flex justify-center items-center">{Number(row.original?.is_assigned) === 0 ? (
      <Link href="#" className={badgeVariants({ variant: "danger" })}>Un-Assigned</Link>

    ) : (
      <Link href="#" className={badgeVariants({ variant: "success" })}>Assigned</Link>
    )}</div>,
  },
  {
    accessorKey: "created_at",
    header: ({ column }) => {
      return (
        <Button
          variant="ghost"
          onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
        >
          Submitted
          <ArrowUpDown />
        </Button>
      )
    },
    cell: ({ row }) => <div className="">{formatDate(row.original.created_at ?? '')}</div>,
  },
  {
    accessorKey: "updated_at",
    header: ({ column }) => {
      return (
        <Button
          variant="ghost"
          onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
        >
          Updated
          <ArrowUpDown />
        </Button>
      )
    },
    cell: ({ row }) => <div className="">{dateDiff(row.original.updated_at ?? '')}</div>,
  },
  {
    id: "actions",
    enableHiding: false,
    cell: ({ row }) => {
      const arrivalNote = row.original;

      const assignSupervisor = useAssignSupervisor();
      const [openDialog, setOpenDialog] = useState(false);
      const {data: supervisors } = useFetchSupervisor();
      const [supervisorList, setSupervisorList] = useState<Supervisor[]>([]);
      const [selectedSupervisor, setSelectedSupervisor] = useState<Supervisor | null>(null);

      interface Supervisor {
        value: string;
        label: string;
      }

      useEffect(() => {
        if (supervisors) {
            const formattedSupervisors: Supervisor[] = supervisors.map((supervisor: { id: string; first_name: string; last_name: string; }) => ({
            value: supervisor.id,
            label: `${supervisor.first_name} ${supervisor.last_name}`,
            }));
          setSupervisorList(formattedSupervisors);
        }
      },[supervisors])
  

      const handleSupervisorChange = (selected: Supervisor): void => {
        console.log("Selected Supervisor:", selected.value , arrivalNote?.student_id, arrivalNote?.id , arrivalNote?.student_id);
        setSelectedSupervisor(selected);
      };

      const handleSaveChanges = (): void => {
        if (selectedSupervisor) {

          assignSupervisor.mutate({
            student_id: arrivalNote?.student_id ?? "",
            supervisor_id: selectedSupervisor?.value,
            arrivalNote_id: arrivalNote?.id ?? "",
          });

          setOpenDialog(false);
        }
      };

      return (
        <>
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" className="h-8 w-8 p-0">
                <span className="sr-only">Open menu</span>
                <MoreHorizontal />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuLabel>Actions</DropdownMenuLabel>
              <DropdownMenuSeparator />
              <Link href={`/admin/arrival_notes/${arrivalNote?.id}`}>
                <DropdownMenuItem>View Arrival Note</DropdownMenuItem>
              </Link>
              <DropdownMenuItem onClick={() => setOpenDialog(true)}>
                Assign Supervisor
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
  
          {/* Move Dialog outside the dropdown */}
          <Dialog open={openDialog} onOpenChange={setOpenDialog}>
            <DialogContent className="sm:max-w-[425px]">
              <DialogHeader>
                <DialogTitle>Assign Supervisor</DialogTitle>
                <DialogDescription>
                  Select a supervisor from the dropdown and assign them.
                </DialogDescription>
              </DialogHeader>
              <div className="flex justify-center items-center py-4">
                {/* Inject your Combobox or custom form here */}
                {supervisorList.length === 0 ? (
                  <div className="text-sm text-gray-500">No supervisors available.</div>
                ) : (
                  <Combobox
                  items={supervisorList}
                  placeholder="Select supervisor"
                  onChange={(selected) => handleSupervisorChange(selected as Supervisor)}
                  />
                )}
              </div>
              <DialogFooter>
                <Button onClick={handleSaveChanges}>Save changes</Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>
        </>
      );
    },
  }
  
]