"use client"

import { useState, useEffect } from "react"
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Badge } from "@/components/ui/badge"
import { FileUploadComponent } from "./file-upload"
import { RichTextEditor } from "./rich-text-editor"
import { Logbook, FileUpload as FileUploadType } from "../types/logbook"
import { useGlobalManager } from "@/hooks/use-context"
import { 
  Save, 
  Send, 
  Calendar, 
  Clock, 
  FileText, 
  Target,
  AlertTriangle,
  BookOpen,
  Upload
} from "lucide-react"

interface LogbookFormProps {
  initialData?: Logbook
  onSave: (logbook: Logbook) => void
  onSubmit: (logbook: Logbook) => void
  loading?: boolean
}

interface FormData {
  week_number: number
  title: string
  hours_logged: number
}

export function LogbookForm({
  initialData,
  onSave,
  onSubmit,
  loading = false
}: LogbookFormProps) {
  const { user } = useGlobalManager()
  const [content, setContent] = useState(initialData?.content || "")
  const [activities, setActivities] = useState(initialData?.activities || "")
  const [challenges, setChallenges] = useState(initialData?.challenges || "")
  const [learningOutcomes, setLearningOutcomes] = useState(initialData?.learning_outcomes || "")
  const [uploadedFile, setUploadedFile] = useState<FileUploadType | undefined>()
  const [errors, setErrors] = useState<Record<string, string>>({})

  useEffect(() => {
    if (initialData) {
      setContent(initialData.content || "")
      setActivities(initialData.activities || "")
      setChallenges(initialData.challenges || "")
      setLearningOutcomes(initialData.learning_outcomes || "")
      
      // Handle existing file
      if (initialData.file_url && initialData.file_name) {
        setUploadedFile({
          file: new File([], initialData.file_name, { type: initialData.file_type || 'application/octet-stream' }),
          preview: initialData.file_url,
          progress: 100
        })
      } else {
        setUploadedFile(undefined)
      }
    }
  }, [initialData])

  const validateLogbook = () => {
    const newErrors: Record<string, string> = {}

    if (!content.trim() && !activities.trim() && !challenges.trim() && !learningOutcomes.trim() && !uploadedFile) {
      newErrors.content = "Please provide some content for your logbook entry"
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleSaveLogbook = (formData: FormData) => {
    if (!validateLogbook()) return

    const logbookData: Logbook = {
      ...initialData,
      week_number: formData.week_number,
      title: formData.title,
      content: content,
      activities: activities,
      challenges: challenges,
      learning_outcomes: learningOutcomes,
      file_url: uploadedFile?.preview || initialData?.file_url,
      file_name: uploadedFile?.file.name || initialData?.file_name,
      file_size: uploadedFile?.file.size || initialData?.file_size,
      file_type: uploadedFile?.file.type || initialData?.file_type,
      hours_logged: formData.hours_logged,
      status: 'draft',
      is_submitted: false,
      student_id: user?.id || ""
    }
    
    onSave(logbookData)
  }

  const handleSubmitLogbook = (formData: FormData) => {
    if (!validateLogbook()) return

    const logbookData: Logbook = {
      ...initialData,
      week_number: formData.week_number,
      title: formData.title,
      content: content,
      activities: activities,
      challenges: challenges,
      learning_outcomes: learningOutcomes,
      file_url: uploadedFile?.preview || initialData?.file_url,
      file_name: uploadedFile?.file.name || initialData?.file_name,
      file_size: uploadedFile?.file.size || initialData?.file_size,
      file_type: uploadedFile?.file.type || initialData?.file_type,
      hours_logged: formData.hours_logged,
      status: 'submitted',
      is_submitted: true,
      submitted_at: new Date().toISOString(),
      student_id: user?.id || ""
    }
    
    onSubmit(logbookData)
  }

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'draft':
        return <Badge variant="secondary">Draft</Badge>
      case 'submitted':
        return <Badge variant="default">Submitted</Badge>
      case 'reviewed':
        return <Badge variant="outline">Reviewed</Badge>
      case 'approved':
        return <Badge className="bg-green-100 text-green-800">Approved</Badge>
      case 'needs_revision':
        return <Badge variant="destructive">Needs Revision</Badge>
      default:
        return <Badge variant="secondary">{status}</Badge>
    }
  }

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center gap-2">
              <BookOpen className="h-5 w-5" />
              {initialData?.id ? 'Edit Logbook Entry' : 'New Logbook Entry'}
            </CardTitle>
            {initialData?.status && getStatusBadge(initialData.status)}
          </div>
        </CardHeader>
        <CardContent>
          <form
            onSubmit={(e) => {
              e.preventDefault()
              const formData = new FormData(e.currentTarget)
              const data = {
                week_number: parseInt(formData.get('week_number') as string),
                title: formData.get('title') as string,
                hours_logged: parseFloat(formData.get('hours_logged') as string) || 0
              }
              
              const submitter = (e.nativeEvent as SubmitEvent).submitter as HTMLButtonElement
              if (submitter?.name === 'submit') {
                handleSubmitLogbook(data)
              } else {
                handleSaveLogbook(data)
              }
            }}
            className="space-y-6"
          >
            {/* Basic Information */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="space-y-2">
                <Label htmlFor="week_number" className="flex items-center gap-2">
                  <Calendar className="h-4 w-4" />
                  Week Number
                </Label>
                <Input
                  id="week_number"
                  name="week_number"
                  type="number"
                  min="1"
                  max="52"
                  defaultValue={initialData?.week_number || 1}
                  required
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="hours_logged" className="flex items-center gap-2">
                  <Clock className="h-4 w-4" />
                  Hours Logged
                </Label>
                <Input
                  id="hours_logged"
                  name="hours_logged"
                  type="number"
                  step="0.5"
                  min="0"
                  max="168"
                  defaultValue={initialData?.hours_logged || 0}
                  placeholder="0.0"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="title">Entry Title</Label>
                <Input
                  id="title"
                  name="title"
                  defaultValue={initialData?.title || ""}
                  placeholder="Week summary title"
                  required
                />
              </div>
            </div>

            {/* Content Sections */}
            <div className="space-y-6">
              <div className="space-y-2">
                <Label className="flex items-center gap-2">
                  <FileText className="h-4 w-4" />
                  General Content & Reflections
                </Label>
                <RichTextEditor
                  content={content}
                  onChange={setContent}
                  placeholder="Describe your week, experiences, and reflections..."
                />
                {errors.content && (
                  <p className="text-sm text-destructive flex items-center gap-1">
                    <AlertTriangle className="h-4 w-4" />
                    {errors.content}
                  </p>
                )}
              </div>

              <div className="space-y-2">
                <Label className="flex items-center gap-2">
                  <Target className="h-4 w-4" />
                  Activities & Tasks Completed
                </Label>
                <Textarea
                  value={activities}
                  onChange={(e) => setActivities(e.target.value)}
                  placeholder="List the main activities and tasks you completed this week..."
                  rows={4}
                />
              </div>

              <div className="space-y-2">
                <Label className="flex items-center gap-2">
                  <AlertTriangle className="h-4 w-4" />
                  Challenges & Difficulties
                </Label>
                <Textarea
                  value={challenges}
                  onChange={(e) => setChallenges(e.target.value)}
                  placeholder="Describe any challenges or difficulties you encountered..."
                  rows={4}
                />
              </div>

              <div className="space-y-2">
                <Label className="flex items-center gap-2">
                  <BookOpen className="h-4 w-4" />
                  Learning Outcomes & Skills Developed
                </Label>
                <Textarea
                  value={learningOutcomes}
                  onChange={(e) => setLearningOutcomes(e.target.value)}
                  placeholder="What did you learn? What skills did you develop or improve?"
                  rows={4}
                />
              </div>

              {/* File Upload */}
              <div className="space-y-2">
                <Label className="flex items-center gap-2">
                  <Upload className="h-4 w-4" />
                  Supporting Documents (Optional)
                </Label>
                <FileUploadComponent
                  onFileSelect={setUploadedFile}
                  onFileRemove={() => setUploadedFile(undefined)}
                  currentFile={uploadedFile}
                  acceptedTypes={['.pdf', '.doc', '.docx', '.txt', '.jpg', '.jpeg', '.png']}
                />
              </div>
            </div>

            {/* Supervisor Feedback (if exists) */}
            {initialData?.supervisor_feedback && (
              <div className="space-y-2">
                <Label>Supervisor Feedback</Label>
                <div className="p-4 bg-muted rounded-lg">
                  <p className="text-sm">{initialData.supervisor_feedback}</p>
                  {initialData.reviewed_at && (
                    <p className="text-xs text-muted-foreground mt-2">
                      Reviewed on {new Date(initialData.reviewed_at).toLocaleDateString()}
                    </p>
                  )}
                </div>
              </div>
            )}

            {/* Action Buttons */}
            <div className="flex items-center justify-between pt-6 border-t">
              <div className="text-sm text-muted-foreground">
                {initialData?.created_at && (
                  <span>Created: {new Date(initialData.created_at).toLocaleDateString()}</span>
                )}
                {initialData?.updated_at && initialData.created_at !== initialData.updated_at && (
                  <span className="ml-4">
                    Last updated: {new Date(initialData.updated_at).toLocaleDateString()}
                  </span>
                )}
              </div>

              <div className="flex items-center space-x-3">
                <Button
                  type="submit"
                  name="save"
                  variant="outline"
                  disabled={loading}
                  className="flex items-center gap-2"
                >
                  <Save className="h-4 w-4" />
                  Save Draft
                </Button>
                
                <Button
                  type="submit"
                  name="submit"
                  disabled={loading || initialData?.is_submitted}
                  className="flex items-center gap-2"
                >
                  <Send className="h-4 w-4" />
                  Submit Entry
                </Button>
              </div>
            </div>
          </form>
        </CardContent>
      </Card>
    </div>
  )
}
