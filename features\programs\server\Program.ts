import axios from "axios";
import { toast } from "sonner";

export class Program {
  public static api_url = process.env.NEXT_PUBLIC_API_URL;

  public static async index() {

    try {
      const response = await axios.get(`${this.api_url}/other/getPrograms`);

      if (response.status === 200) {
        toast.success(response.data.message);

        return response.data; 
      }

    } catch (error: any) {
      throw error.response.data; 
    }
  }
}
