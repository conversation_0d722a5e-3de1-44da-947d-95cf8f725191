import { useQuery } from "@tanstack/react-query";
import { ArrivalNote } from "../server/ArrivalNote";

export const useRetrieveArrivalNote = (id: string) => {
    const query = useQuery({
        enabled: !!id,
        queryKey: ["arrival_notes", id],
        queryFn: async () => {
          const res = await ArrivalNote.findByUserId(id);
    
          // if (!res.ok) {
          //   return null;
          // }
          console.log('API=>',res)
          return res[0];
        },
    });
    
    return query;
}