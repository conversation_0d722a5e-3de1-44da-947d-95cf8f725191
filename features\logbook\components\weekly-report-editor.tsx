"use client"

import { useState, useEffect } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON>Content, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { Badge } from "@/components/ui/badge"
import { WeeklyReport, FileUpload } from "../types/logbook"
import { RichTextEditor } from "./rich-text-editor"
import { FileUploadComponent } from "./file-upload"
import { ConfirmationDialog } from "./confirmation-dialog"
import { toast } from "sonner"
import { Save, Send, FileText, Edit3 } from "lucide-react"

interface WeeklyReportEditorProps {
  weekNumber: number
  initialReport?: WeeklyReport
  onSave: (report: WeeklyReport) => void
  onSubmit?: (report: WeeklyReport) => void
  disabled?: boolean
}

export function WeeklyReportEditor({
  weekNumber,
  initialReport,
  onSave,
  onSubmit,
  disabled = false
}: WeeklyReportEditorProps) {
  const [submissionType, setSubmissionType] = useState<'write' | 'upload'>(
    initialReport?.submission_type || 'write'
  )
  const [content, setContent] = useState(initialReport?.content || "")
  const [uploadedFile, setUploadedFile] = useState<FileUpload | undefined>()
  const [showSubmitDialog, setShowSubmitDialog] = useState(false)
  const [saving, setSaving] = useState(false)

  // Initialize state from initial report
  useEffect(() => {
    if (initialReport) {
      setSubmissionType(initialReport.submission_type)
      setContent(initialReport.content || "")

      // If there's a file, create a mock FileUpload object
      if (initialReport.file_url && initialReport.file_name) {
        // Note: In a real implementation, you'd fetch the actual file
        setUploadedFile({
          file: new File([], initialReport.file_name),
          preview: initialReport.file_url
        })
      }
    }
  }, [initialReport])

  const validateReport = (showToast: boolean = true) => {
    if (submissionType === 'write') {
      const textContent = content.replace(/<[^>]*>/g, '').trim()
      if (textContent.length < 100) {
        if (showToast) {
          toast.error("Report must be at least 100 characters long")
        }
        return false
      }
    } else if (submissionType === 'upload') {
      if (!uploadedFile) {
        if (showToast) {
          toast.error("Please upload a file for your report")
        }
        return false
      }
    }
    return true
  }

  const handleSave = async () => {
    if (!validateReport()) return

    setSaving(true)
    try {
      const report: WeeklyReport = {
        ...initialReport,
        week_number: weekNumber,
        submission_type: submissionType,
        content: submissionType === 'write' ? content : undefined,
        file_name: submissionType === 'upload' ? uploadedFile?.file.name : undefined,
        file_size: submissionType === 'upload' ? uploadedFile?.file.size : undefined,
        file_type: submissionType === 'upload' ? uploadedFile?.file.type : undefined,
        character_count: submissionType === 'write' ? content.replace(/<[^>]*>/g, '').length : undefined,
        is_submitted: false
      }

      await onSave(report)
      toast.success("Report saved successfully")
    } catch (error) {
      toast.error("Failed to save report")
    } finally {
      setSaving(false)
    }
  }

  const handleSubmit = async () => {
    if (!validateReport()) return

    setSaving(true)
    try {
      const report: WeeklyReport = {
        ...initialReport,
        week_number: weekNumber,
        submission_type: submissionType,
        content: submissionType === 'write' ? content : undefined,
        file_name: submissionType === 'upload' ? uploadedFile?.file.name : undefined,
        file_size: submissionType === 'upload' ? uploadedFile?.file.size : undefined,
        file_type: submissionType === 'upload' ? uploadedFile?.file.type : undefined,
        character_count: submissionType === 'write' ? content.replace(/<[^>]*>/g, '').length : undefined,
        is_submitted: true
      }

      if (onSubmit) {
        await onSubmit(report)
      } else {
        await onSave(report)
      }

      toast.success("Report submitted successfully")
    } catch (error) {
      toast.error("Failed to submit report")
    } finally {
      setSaving(false)
    }
  }

  const handleFileSelect = (file: FileUpload) => {
    setUploadedFile(file)
  }

  const handleFileRemove = () => {
    setUploadedFile(undefined)
  }

  const isSubmitted = initialReport?.is_submitted || false
  const canEdit = !isSubmitted && !disabled

  return (
    <>
      <Card className="mt-6">
        <CardHeader>
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <CardTitle>Week {weekNumber} Report</CardTitle>
              {isSubmitted && (
                <Badge variant="secondary" className="bg-green-100 text-green-800">
                  Submitted
                </Badge>
              )}
            </div>

            {submissionType === 'write' && (
              <div className="flex items-center space-x-2 text-sm text-muted-foreground">
                <Edit3 className="h-4 w-4" />
                <span>{content.replace(/<[^>]*>/g, '').length} characters</span>
              </div>
            )}

            {submissionType === 'upload' && uploadedFile && (
              <div className="flex items-center space-x-2 text-sm text-muted-foreground">
                <FileText className="h-4 w-4" />
                <span>{uploadedFile.file.name}</span>
              </div>
            )}
          </div>
        </CardHeader>

        <CardContent>
          <Tabs
            value={submissionType}
            onValueChange={(value) => canEdit && setSubmissionType(value as 'write' | 'upload')}
            className="w-full"
          >
            <TabsList className="grid w-full grid-cols-2">
              <TabsTrigger value="write" disabled={!canEdit}>
                <Edit3 className="h-4 w-4 mr-2" />
                Write Report
              </TabsTrigger>
              <TabsTrigger value="upload" disabled={!canEdit}>
                <FileText className="h-4 w-4 mr-2" />
                Upload File
              </TabsTrigger>
            </TabsList>

            <TabsContent value="write" className="mt-6">
              <RichTextEditor
                value={content}
                onChange={setContent}
                placeholder="Write your weekly report here. Describe your activities, learnings, challenges, and achievements for this week..."
                minHeight="400px"
                maxLength={5000}
                showCharacterCount={true}
              />

              <div className="mt-4 text-sm text-muted-foreground">
                <p>Minimum 100 characters required. Use the formatting tools above to structure your report.</p>
              </div>
            </TabsContent>

            <TabsContent value="upload" className="mt-6">
              <FileUploadComponent
                onFileSelect={handleFileSelect}
                onFileRemove={handleFileRemove}
                currentFile={uploadedFile}
                maxSize={10}
                acceptedTypes={['.pdf', '.doc', '.docx']}
              />

              <div className="mt-4 text-sm text-muted-foreground">
                <p>Upload your weekly report as a PDF, DOC, or DOCX file (max 10MB).</p>
              </div>
            </TabsContent>
          </Tabs>

          {canEdit && (
            <div className="flex items-center justify-between mt-6 pt-4 border-t">
              <div className="text-sm text-muted-foreground">
                {/* Validation messages removed from here to prevent constant display */}
                <span>
                  {submissionType === 'write'
                    ? "Use the formatting tools above to structure your report."
                    : "Supported formats: PDF, DOC, DOCX (max 10MB)"
                  }
                </span>
              </div>

              <div className="flex items-center space-x-3">
                <Button
                  variant="outline"
                  onClick={handleSave}
                  disabled={saving}
                  className="flex items-center space-x-2"
                >
                  <Save className="h-4 w-4" />
                  <span>{saving ? "Saving..." : "Save Draft"}</span>
                </Button>

                <Button
                  onClick={() => setShowSubmitDialog(true)}
                  disabled={saving || !validateReport(false)}
                  className="flex items-center space-x-2"
                >
                  <Send className="h-4 w-4" />
                  <span>Submit Report</span>
                </Button>
              </div>
            </div>
          )}

          {isSubmitted && (
            <div className="mt-6 pt-4 border-t">
              <div className="flex items-center justify-center text-sm text-muted-foreground">
                <span>This report has been submitted and cannot be edited.</span>
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      <ConfirmationDialog
        isOpen={showSubmitDialog}
        onClose={() => setShowSubmitDialog(false)}
        onConfirm={handleSubmit}
        title="Submit Weekly Report"
        description="Are you sure you want to submit this report? Once submitted, you won't be able to make any changes."
        confirmText="Submit Report"
        cancelText="Cancel"
      />
    </>
  )
}