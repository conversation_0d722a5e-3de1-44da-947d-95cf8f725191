import { useMutation, useQueryClient } from "@tanstack/react-query";
import { ArrivalNote } from "../server/ArrivalNote";
import { toast } from "sonner";


export const useAssignSupervisor = () => {
    const queryClient = useQueryClient();

    const mutation = useMutation({
        mutationFn: async (data: {student_id:string; supervisor_id:string; arrivalNote_id: string}) => {
            const res = await ArrivalNote.assign(data.student_id, data.supervisor_id, data.arrivalNote_id);
            return res;
        },
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ["arrival_notes"] });
        },
        onError: (error) => {
            toast.error(error.message);
        },
    });

    return mutation;
}