"use client"

import { useState } from "react"
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON>er, Card<PERSON>itle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { ConfirmationDialog } from "@/features/logbook/components/confirmation-dialog"
import { <PERSON><PERSON>, <PERSON>alogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { Feedback } from "../types/reports"
import {
  Edit,
  Trash2,
  FileText,
  Star,
  Calendar,
  MessageSquare,
  Upload,
  Eye,
  Download
} from "lucide-react"

interface FeedbackListProps {
  feedbackList: Feedback[]
  onEdit: (feedback: Feedback) => void
  onDelete: (id: string) => void
  loading?: boolean
}

interface FilePreviewDialogProps {
  isOpen: boolean
  onClose: () => void
  feedback: Feedback | null
}

function FilePreviewDialog({ isOpen, onClose, feedback }: FilePreviewDialogProps) {
  if (!feedback) return null

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes'
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  const handleDownload = () => {
    if (feedback.file_url) {
      const link = document.createElement('a')
      link.href = feedback.file_url
      link.download = feedback.file_name || 'feedback-file'
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
    }
  }

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[80vh]">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <FileText className="h-5 w-5" />
            File Preview: {feedback.file_name}
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-4">
          {/* File Info */}
          <div className="flex items-center justify-between p-4 bg-muted rounded-lg">
            <div className="space-y-1">
              <p className="font-medium">{feedback.file_name}</p>
              <p className="text-sm text-muted-foreground">
                {feedback.file_type} • {feedback.file_size ? formatFileSize(feedback.file_size) : 'Unknown size'}
              </p>
            </div>
            <Button onClick={handleDownload} variant="outline" size="sm">
              <Download className="h-4 w-4 mr-2" />
              Download
            </Button>
          </div>

          {/* File Preview */}
          <div className="border rounded-lg overflow-hidden" style={{ height: '500px' }}>
            {feedback.file_type === 'application/pdf' && feedback.file_url ? (
              <iframe
                src={feedback.file_url}
                className="w-full h-full"
                title="PDF Preview"
              />
            ) : (
              <div className="flex items-center justify-center h-full bg-muted">
                <div className="text-center">
                  <FileText className="h-16 w-16 mx-auto mb-4 text-muted-foreground" />
                  <p className="text-lg font-medium">Preview not available</p>
                  <p className="text-sm text-muted-foreground">
                    This file type cannot be previewed in the browser
                  </p>
                  <Button onClick={handleDownload} className="mt-4">
                    <Download className="h-4 w-4 mr-2" />
                    Download to view
                  </Button>
                </div>
              </div>
            )}
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
}

export function FeedbackList({
  feedbackList,
  onEdit,
  onDelete,
  loading = false
}: FeedbackListProps) {
  const [deleteConfirm, setDeleteConfirm] = useState<string | null>(null)
  const [previewDialog, setPreviewDialog] = useState<{
    isOpen: boolean
    feedback: Feedback | null
  }>({
    isOpen: false,
    feedback: null
  })

  const handleDelete = (id: string) => {
    setDeleteConfirm(id)
  }

  const confirmDelete = () => {
    if (deleteConfirm) {
      onDelete(deleteConfirm)
      setDeleteConfirm(null)
    }
  }

  const handlePreview = (feedback: Feedback) => {
    setPreviewDialog({
      isOpen: true,
      feedback
    })
  }

  const closePreview = () => {
    setPreviewDialog({
      isOpen: false,
      feedback: null
    })
  }

  const getCategoryLabel = (category: string) => {
    const labels: Record<string, string> = {
      'general': 'General Experience',
      'program': 'Program Structure',
      'supervision': 'Supervision & Mentoring',
      'facilities': 'Facilities & Resources',
      'recommendations': 'Recommendations'
    }
    return labels[category] || category
  }

  const getCategoryColor = (category: string) => {
    const colors: Record<string, string> = {
      'general': 'bg-blue-100 text-blue-800',
      'program': 'bg-green-100 text-green-800',
      'supervision': 'bg-purple-100 text-purple-800',
      'facilities': 'bg-orange-100 text-orange-800',
      'recommendations': 'bg-pink-100 text-pink-800'
    }
    return colors[category] || 'bg-gray-100 text-gray-800'
  }

  const renderStars = (rating: number) => {
    return (
      <div className="flex items-center space-x-1">
        {[1, 2, 3, 4, 5].map((star) => (
          <Star
            key={star}
            className={`h-4 w-4 ${
              star <= rating ? 'text-yellow-500 fill-current' : 'text-gray-300'
            }`}
          />
        ))}
        <span className="ml-1 text-sm text-muted-foreground">({rating}/5)</span>
      </div>
    )
  }

  if (feedbackList.length === 0) {
    return (
      <Card className="p-8 text-center">
        <CardContent className="p-0">
          <div className="text-muted-foreground">
            <MessageSquare className="h-12 w-12 mx-auto mb-4 opacity-50" />
            <p className="text-lg font-medium mb-2">No feedback yet</p>
            <p className="text-sm">Start by adding your first feedback or recommendation</p>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <>
      <div className="space-y-4">
        {feedbackList.map((feedback) => (
          <Card key={feedback.id} className="w-full">
            <CardHeader>
              <div className="flex items-start justify-between">
                <div className="space-y-2">
                  <div className="flex items-center space-x-2">
                    <CardTitle className="text-lg">{feedback.title}</CardTitle>
                    <Badge className={getCategoryColor(feedback.category)}>
                      {getCategoryLabel(feedback.category)}
                    </Badge>
                    {feedback.is_submitted ? (
                      <Badge variant="secondary" className="bg-green-100 text-green-800">
                        Submitted
                      </Badge>
                    ) : (
                      <Badge variant="outline">Draft</Badge>
                    )}
                  </div>
                  
                  {feedback.rating && (
                    <div className="flex items-center space-x-2">
                      <span className="text-sm text-muted-foreground">Rating:</span>
                      {renderStars(feedback.rating)}
                    </div>
                  )}
                </div>

                <div className="flex items-center space-x-2">
                  {feedback.submission_type === 'upload' && feedback.file_name && (
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handlePreview(feedback)}
                      className="h-8 w-8 p-0"
                      title="Preview file"
                    >
                      <Eye className="h-4 w-4" />
                    </Button>
                  )}

                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => onEdit(feedback)}
                    disabled={loading}
                    className="h-8 w-8 p-0"
                    title="Edit feedback"
                  >
                    <Edit className="h-4 w-4" />
                  </Button>

                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleDelete(feedback.id!)}
                    disabled={loading}
                    className="h-8 w-8 p-0 text-destructive hover:text-destructive"
                    title="Delete feedback"
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </CardHeader>

            <CardContent>
              <div className="space-y-3">
                {/* Content Preview */}
                {feedback.submission_type === 'write' && feedback.content && (
                  <div className="space-y-2">
                    <div className="flex items-center space-x-2 text-sm text-muted-foreground">
                      <FileText className="h-4 w-4" />
                      <span>Written feedback ({feedback.character_count} characters)</span>
                    </div>
                    <div 
                      className="text-sm text-muted-foreground line-clamp-3 p-3 bg-muted rounded-lg"
                      dangerouslySetInnerHTML={{ 
                        __html: feedback.content.length > 200 
                          ? feedback.content.substring(0, 200) + '...' 
                          : feedback.content 
                      }}
                    />
                  </div>
                )}

                {feedback.submission_type === 'upload' && feedback.file_name && (
                  <div className="space-y-2">
                    <div className="flex items-center space-x-2 text-sm text-muted-foreground">
                      <Upload className="h-4 w-4" />
                      <span>Uploaded file</span>
                    </div>
                    <div className="flex items-center justify-between p-3 bg-muted rounded-lg">
                      <div className="flex items-center space-x-2">
                        <FileText className="h-5 w-5 text-blue-500" />
                        <div>
                          <p className="font-medium text-sm">{feedback.file_name}</p>
                          {feedback.file_size && (
                            <p className="text-xs text-muted-foreground">
                              {(feedback.file_size / 1024 / 1024).toFixed(2)} MB
                            </p>
                          )}
                        </div>
                      </div>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handlePreview(feedback)}
                        className="h-8 px-2"
                      >
                        <Eye className="h-4 w-4 mr-1" />
                        Preview
                      </Button>
                    </div>
                  </div>
                )}

                {/* Metadata */}
                <div className="flex items-center justify-between text-xs text-muted-foreground pt-2 border-t">
                  <div className="flex items-center space-x-4">
                    {feedback.created_at && (
                      <div className="flex items-center space-x-1">
                        <Calendar className="h-3 w-3" />
                        <span>Created: {new Date(feedback.created_at).toLocaleDateString()}</span>
                      </div>
                    )}
                    
                    {feedback.submitted_at && (
                      <div className="flex items-center space-x-1">
                        <Calendar className="h-3 w-3" />
                        <span>Submitted: {new Date(feedback.submitted_at).toLocaleDateString()}</span>
                      </div>
                    )}
                  </div>

                  {feedback.is_submitted && (
                    <span className="text-green-600 font-medium">✓ Submitted</span>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      <ConfirmationDialog
        isOpen={!!deleteConfirm}
        onClose={() => setDeleteConfirm(null)}
        onConfirm={confirmDelete}
        title="Delete Feedback"
        description="Are you sure you want to delete this feedback? This action cannot be undone."
        confirmText="Delete"
        cancelText="Cancel"
        variant="destructive"
      />

      <FilePreviewDialog
        isOpen={previewDialog.isOpen}
        onClose={closePreview}
        feedback={previewDialog.feedback}
      />
    </>
  )
}
