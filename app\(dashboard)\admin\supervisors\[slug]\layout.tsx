import { DottedSeparator } from '@/components/dotted-separator'
import { But<PERSON> } from '@/components/ui/button';
import { Supervisor } from '@/features/supervisors/server/Supervisor';
import { LogOut } from 'lucide-react';
import Link from 'next/link';
import React, { ReactNode } from 'react'

const SingleSupervisorLayourPage = async ({
    children , params
} : {
    children: ReactNode,
    params : {slug: string}
}) => {

    const slug = (await params).slug;

    const response =await Supervisor.show(slug);

    if (!response.success) {
        return (
        <div className="flex flex-1 items-center justify-center">
            <p className="text-muted-foreground">No Supervisor Data found.</p>
        </div>
        )
    }

    const supervisor = response.data[0];

  return (
    <div className="flex flex-1 flex-col">
      <div className="@container/main flex flex-1 flex-col gap-2">
        <div className="flex flex-col gap-4 py-4 md:gap-6 md:py-6">
          <div className="flex flex-col gap-4 px-4 lg:px-6">
            <div className="flex flex-col gap-2">
                <div className="flex justify-between items-center">
                    <div className="">
                        <h1 className="text-2xl font-bold capitalize">{supervisor.first_name + ' ' + supervisor.last_name}</h1>
                        <p className="text-muted-foreground">
                            Here is where you can manage single supervisor.
                        </p>
                    </div>

                    <div className="">
                        <Link href={`/admin/supervisors`} >
                        <Button variant="outline" className="flex items-center gap-2" >
                            <span>Exit</span>
                             <LogOut size={12}/>
                        </Button>
                        </Link>
                    </div>
                </div>
              <DottedSeparator />
            </div>

            {children}
            
          </div>
          </div>
          </div>
      </div>
  )
}

export default SingleSupervisorLayourPage
