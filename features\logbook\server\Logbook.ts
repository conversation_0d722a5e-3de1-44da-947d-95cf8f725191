import axios from "axios";
import { toast } from "sonner";
import { LogDay, WeeklyReport } from "../types/logbook";

export class Logbook {
  public static api_url = process.env.NEXT_PUBLIC_API_URL;

  public static async getDailyLogs(weekNumber: number, student_id?: string) {
    try {
      const response = await axios.get(`${this.api_url}/logbook/daily-logs`, {
        params: { week_number: weekNumber, student_id }
      });

      if (response.status === 200) {
        return response.data;
      }
    } catch (error: any) {
      toast.error(error.response?.data?.message || "Failed to fetch daily logs");
      throw error.response?.data;
    }
  }

  public static async createDailyLog(data: LogDay) {
    try {
      const response = await axios.post(`${this.api_url}/logbook/daily-logs`, data);

      if (response.status === 200) {
        toast.success(response.data.message || "Daily log created successfully");
        return response.data;
      }
    } catch (error: any) {
      toast.error(error.response?.data?.message || "Failed to create daily log");
      throw error.response?.data;
    }
  }

  public static async updateDailyLog(id: string, data: Partial<LogDay>) {
    try {
      const response = await axios.put(`${this.api_url}/logbook/daily-logs/${id}`, data);

      if (response.status === 200) {
        toast.success(response.data.message || "Daily log updated successfully");
        return response.data;
      }
    } catch (error: any) {
      toast.error(error.response?.data?.message || "Failed to update daily log");
      throw error.response?.data;
    }
  }

  public static async getWeeklyReport(weekNumber: number, student_id?: string) {
    try {
      const response = await axios.get(`${this.api_url}/logbook/weekly-reports`, {
        params: { week_number: weekNumber, student_id }
      });

      if (response.status === 200) {
        return response.data;
      }
    } catch (error: any) {
      toast.error(error.response?.data?.message || "Failed to fetch weekly report");
      throw error.response?.data;
    }
  }

  public static async saveWeeklyReport(data: WeeklyReport) {
    try {
      const response = await axios.post(`${this.api_url}/logbook/weekly-reports`, data);

      if (response.status === 200) {
        const message = data.is_submitted
          ? "Weekly report submitted successfully"
          : "Weekly report saved successfully";
        toast.success(response.data.message || message);
        return response.data;
      }
    } catch (error: any) {
      const message = data.is_submitted
        ? "Failed to submit weekly report"
        : "Failed to save weekly report";
      toast.error(error.response?.data?.message || message);
      throw error.response?.data;
    }
  }

  public static async uploadFile(file: File, weekNumber: number) {
    try {
      const formData = new FormData();
      formData.append('file', file);
      formData.append('week_number', weekNumber.toString());

      const response = await axios.post(`${this.api_url}/logbook/upload`, formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });

      if (response.status === 200) {
        toast.success("File uploaded successfully");
        return response.data;
      }
    } catch (error: any) {
      toast.error(error.response?.data?.message || "Failed to upload file");
      throw error.response?.data;
    }
  }
}