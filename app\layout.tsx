"use client";
/** @jsxImportSource react */

import { Toaster } from "@/components/ui/sonner"
import "./globals.css";
import { ThemeProvider } from "@/components/theme-provider";
import { Separator } from "@/components/ui/separator"
import {
  Sidebar<PERSON>nset,
  SidebarProvider,
  SidebarTrigger,
} from "@/components/ui/sidebar"
import { ModeToggle } from "@/components/mode-toggler";
import { AppSidebar } from "@/components/app-sidebar";
import { GlobalManagerProvider, useGlobalManager } from "@/hooks/use-context";
import { LoginForm } from "@/features/auth/components/login-form";
import { GalleryVerticalEnd } from "lucide-react";
import { QueryProvider } from "@/components/query-provider";
import ProgressBar from "@/components/progress-bar";
import { useState } from "react";
import { RegisterForm } from "@/features/auth/components/register-form";
import { Badge } from "@/components/ui/badge";
import SystemFormModals from "@/components/SystemFormModals";
import { NuqsAdapter } from 'nuqs/adapters/next/app'
import { useFetchIPTs } from "@/features/ipt/api/use-fetch-ipts";
import B_Crump from "@/components/bread-crump";
import { useSearchParams } from 'next/navigation'
import { OneTimeLoginForm } from "@/features/supervisors/components/supervisor-login-form";

/**
 * Metadata for the layout
 * For now is not used, because this is client-side rendered
 * and the metadata is not used in the client-side
 * 
  export const metadata: Metadata = {
    title: "DIT IPTMS",
    description: "Industrial Training Management System",
    keywords: "DIT, IPTMS, Industrial Training, Management System",
  };
*/


export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" suppressHydrationWarning>
      <body>
        <ThemeProvider
          attribute="class"
          defaultTheme="system"
          enableSystem
          disableTransitionOnChange
        >
          <QueryProvider>
          <NuqsAdapter>
          <GlobalManagerProvider>
            <DynamicLayout>{children}</DynamicLayout>
            <Toaster richColors />
          </GlobalManagerProvider>
          </NuqsAdapter>
          </QueryProvider>
        </ThemeProvider>
      </body>
    </html>
  );
}

const DynamicLayout = ({ children }: { children: React.ReactNode }) => {
  const { isAuthenticated , loading , isRegistered } = useGlobalManager();
  const [showRegisterForm, setShowRegisterForm] = useState(false);
  const { data: ipts } = useFetchIPTs();
  const searchParams = useSearchParams()
  const tag = searchParams.get('tag');
  const email = searchParams.get('email');

  if (loading) {
    return <ProgressBar loading={loading}/>;
  }else if (loading === false && !isAuthenticated) {
    
    return (
      <div className="flex min-h-screen flex-col items-center justify-center gap-6 bg-muted p-6 md:p-10">
        <div className={`flex w-full flex-col ${showRegisterForm ? 'max-w-md' : 'max-w-sm'} gap-6`}>
          <a href="#" className="flex items-center gap-2 self-center font-medium">
            <div className="flex h-6 w-6 items-center justify-center rounded-md bg-primary text-primary-foreground">
              <GalleryVerticalEnd className="size-4" />
            </div>
            DIT IPTMS. <Badge variant="destructive">
              {ipts && ipts.length > 0 ? ipts[0].name : 'No Ipt'}
            </Badge>
          </a>
          {tag === "otp" ? (
            <div className="flex flex-col items-center gap-6">
              <OneTimeLoginForm email={email ?? undefined}/>
            </div>
          ) : (
            !showRegisterForm || isRegistered ? (
              <div className="flex flex-col items-center gap-6">
                <LoginForm setShowRegisterForm={setShowRegisterForm}/>
              </div>
            ) : (
              <RegisterForm setShowRegisterForm={setShowRegisterForm} ipt_id={ipts && ipts.length > 0 ? ipts[0].id : undefined}/>
            )
          )}
        </div>
      </div>
    );
  }

  return (
    <>
    <SidebarProvider>
      <AppSidebar />
      <SidebarInset>
        <header className="flex h-16 shrink-0 items-center gap-2 transition-[width,height] ease-linear group-has-[[data-collapsible=icon]]/sidebar-wrapper:h-12">
          <div className="flex items-center justify-between w-full">
            <div className="flex items-center gap-2 px-4">
              <SidebarTrigger className="-ml-1" />
              <Separator orientation="vertical" className="mr-2 h-4" />
              <B_Crump />
            </div>
            <div className="ml-auto flex items-center gap-2 pr-8">
              {/* other components */}

              <ModeToggle />
            </div>
          </div>
        </header>
        <div className="flex flex-1 flex-col gap-4 p-4 pt-0">
          {children}
        </div>
      </SidebarInset>
    </SidebarProvider>
    <SystemFormModals />
    </>
  );
};


