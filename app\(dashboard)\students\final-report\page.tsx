"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Textarea } from "@/components/ui/textarea"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Label } from "@/components/ui/label"
import { useGlobalManager } from "@/hooks/use-context"
import { FinalReports, FinalReport } from "@/features/reports/server/FinalReports"
import {
  FileText,
  Upload,
  Save,
  Send,
  Download,
  Edit,
  CheckCircle,
  AlertCircle
} from "lucide-react"

export default function FinalReportPage() {
  const { user } = useGlobalManager()
  const [finalReport, setFinalReport] = useState<FinalReport | null>(null)
  const [loading, setLoading] = useState(true)
  const [saving, setSaving] = useState(false)
  const [submissionType, setSubmissionType] = useState<'write' | 'upload'>('write')
  const [reportData, setReportData] = useState<Partial<FinalReport>>({
    title: 'Final Internship Report',
    submission_type: 'write',
    content: '',
    executive_summary: '',
    objectives_achieved: '',
    challenges_faced: '',
    skills_acquired: '',
    recommendations: '',
    conclusion: '',
    status: 'draft',
    is_submitted: false
  })

  useEffect(() => {
    loadFinalReport()
  }, [user?.id])

  const loadFinalReport = async () => {
    if (!user?.id) return
    
    try {
      setLoading(true)
      const result = await FinalReports.getMyFinalReport()
      if (result?.data) {
        setFinalReport(result.data)
        setReportData(result.data)
        setSubmissionType(result.data.submission_type || 'write')
      }
    } catch (error) {
      // No final report exists yet
      console.log('No final report found, starting fresh')
    } finally {
      setLoading(false)
    }
  }

  const handleSave = async () => {
    try {
      setSaving(true)
      
      const dataToSave = {
        ...reportData,
        submission_type: submissionType,
        student_id: user?.id || "",
        character_count: reportData.content?.length || 0,
        word_count: reportData.content?.split(' ').filter(word => word.length > 0).length || 0
      }

      if (finalReport?.id) {
        await FinalReports.updateFinalReport(finalReport.id, dataToSave)
      } else {
        const result = await FinalReports.createFinalReport(dataToSave as FinalReport)
        if (result?.data) {
          setFinalReport(result.data)
        }
      }
      
      await loadFinalReport()
    } catch (error) {
      console.error('Failed to save final report:', error)
    } finally {
      setSaving(false)
    }
  }

  const handleSubmit = async () => {
    try {
      setSaving(true)
      
      const dataToSubmit = {
        ...reportData,
        submission_type: submissionType,
        student_id: user?.id || "",
        status: 'submitted',
        is_submitted: true,
        submitted_at: new Date().toISOString(),
        character_count: reportData.content?.length || 0,
        word_count: reportData.content?.split(' ').filter(word => word.length > 0).length || 0
      }

      if (finalReport?.id) {
        await FinalReports.updateFinalReport(finalReport.id, dataToSubmit)
      } else {
        const result = await FinalReports.createFinalReport(dataToSubmit as FinalReport)
        if (result?.data) {
          setFinalReport(result.data)
        }
      }
      
      await loadFinalReport()
    } catch (error) {
      console.error('Failed to submit final report:', error)
    } finally {
      setSaving(false)
    }
  }

  const handleFileUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (!file) return

    try {
      setSaving(true)
      // TODO: Implement file upload
      console.log('File upload not yet implemented:', file.name)
    } catch (error) {
      console.error('Failed to upload file:', error)
    } finally {
      setSaving(false)
    }
  }

  const updateReportData = (field: keyof FinalReport, value: any) => {
    setReportData(prev => ({ ...prev, [field]: value }))
  }

  const isSubmitted = finalReport?.is_submitted || false
  const canEdit = !isSubmitted

  if (loading) {
    return (
      <div className="flex flex-1 flex-col">
        <div className="@container/main flex flex-1 flex-col gap-2">
          <div className="flex flex-col gap-4 py-4 md:gap-6 md:py-6">
            <div className="flex flex-col gap-4 px-4 lg:px-6">
              <div className="flex items-center justify-center h-32">
                <div className="text-center">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
                  <p className="text-muted-foreground">Loading final report...</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="flex flex-1 flex-col">
      <div className="@container/main flex flex-1 flex-col gap-2">
        <div className="flex flex-col gap-4 py-4 md:gap-6 md:py-6">
          <div className="flex flex-col gap-4 px-4 lg:px-6">
            
            {/* Header */}
            <div className="flex items-center justify-between">
              <div className="flex flex-col gap-2">
                <h1 className="text-2xl font-bold">Final Internship Report</h1>
                <p className="text-muted-foreground">
                  Submit your comprehensive internship report
                </p>
              </div>
              <div className="flex items-center gap-2">
                {finalReport && (
                  <Badge variant={isSubmitted ? "default" : "secondary"}>
                    {isSubmitted ? "Submitted" : "Draft"}
                  </Badge>
                )}
                {canEdit && (
                  <>
                    <Button
                      variant="outline"
                      onClick={handleSave}
                      disabled={saving}
                      className="flex items-center gap-2"
                    >
                      <Save className="h-4 w-4" />
                      Save Draft
                    </Button>
                    <Button
                      onClick={handleSubmit}
                      disabled={saving || !reportData.content || (reportData.content?.length || 0) < 500}
                      className="flex items-center gap-2"
                    >
                      <Send className="h-4 w-4" />
                      Submit Report
                    </Button>
                  </>
                )}
              </div>
            </div>

            {/* Status Card */}
            {finalReport && (
              <Card>
                <CardContent className="pt-6">
                  <div className="flex items-center gap-4">
                    {isSubmitted ? (
                      <CheckCircle className="h-8 w-8 text-green-500" />
                    ) : (
                      <AlertCircle className="h-8 w-8 text-orange-500" />
                    )}
                    <div>
                      <h3 className="font-medium">
                        {isSubmitted ? 'Report Submitted Successfully' : 'Report in Progress'}
                      </h3>
                      <p className="text-sm text-muted-foreground">
                        {isSubmitted 
                          ? `Submitted on ${new Date(finalReport.submitted_at!).toLocaleDateString()}`
                          : 'Continue working on your final report'
                        }
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Submission Type Selection */}
            {canEdit && (
              <Card>
                <CardHeader>
                  <CardTitle>Submission Method</CardTitle>
                </CardHeader>
                <CardContent>
                  <Tabs value={submissionType} onValueChange={(value) => setSubmissionType(value as 'write' | 'upload')}>
                    <TabsList className="grid w-full grid-cols-2">
                      <TabsTrigger value="write" className="flex items-center gap-2">
                        <FileText className="h-4 w-4" />
                        Write Online
                      </TabsTrigger>
                      <TabsTrigger value="upload" className="flex items-center gap-2">
                        <Upload className="h-4 w-4" />
                        Upload File
                      </TabsTrigger>
                    </TabsList>
                  </Tabs>
                </CardContent>
              </Card>
            )}

            {/* Content */}
            {submissionType === 'write' ? (
              <Card>
                <CardHeader>
                  <CardTitle>Report Content</CardTitle>
                </CardHeader>
                <CardContent className="space-y-6">
                  <div className="space-y-2">
                    <Label htmlFor="title">Report Title</Label>
                    <Input
                      id="title"
                      value={reportData.title || ''}
                      onChange={(e) => updateReportData('title', e.target.value)}
                      disabled={!canEdit}
                      placeholder="Final Internship Report"
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="content">
                      Report Content <span className="text-destructive">*</span>
                    </Label>
                    <Textarea
                      id="content"
                      value={reportData.content || ''}
                      onChange={(e) => updateReportData('content', e.target.value)}
                      disabled={!canEdit}
                      placeholder="Write your comprehensive final report here. Include your experiences, learnings, achievements, and reflections from your internship period. Minimum 500 characters required."
                      rows={15}
                      className="min-h-[400px]"
                    />
                    <div className="text-xs text-muted-foreground">
                      {(reportData.content || '').length} / 500 characters minimum
                    </div>
                  </div>
                </CardContent>
              </Card>
            ) : (
              <Card>
                <CardHeader>
                  <CardTitle>Upload Report File</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="border-2 border-dashed border-muted-foreground/25 rounded-lg p-8 text-center">
                      <Upload className="h-12 w-12 mx-auto mb-4 text-muted-foreground" />
                      <h3 className="text-lg font-medium mb-2">Upload your final report</h3>
                      <p className="text-muted-foreground mb-4">
                        Supported formats: PDF, DOC, DOCX (Max 10MB)
                      </p>
                      <input
                        type="file"
                        accept=".pdf,.doc,.docx"
                        onChange={handleFileUpload}
                        disabled={!canEdit || saving}
                        className="hidden"
                        id="file-upload"
                      />
                      <label htmlFor="file-upload">
                        <Button variant="outline" disabled={!canEdit || saving} asChild>
                          <span>Choose File</span>
                        </Button>
                      </label>
                    </div>
                    
                    {finalReport?.file_name && (
                      <div className="flex items-center gap-2 p-3 bg-muted rounded-lg">
                        <FileText className="h-4 w-4" />
                        <span className="flex-1">{finalReport.file_name}</span>
                        <Button variant="ghost" size="sm">
                          <Download className="h-4 w-4" />
                        </Button>
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Submitted Report View */}
            {isSubmitted && submissionType === 'write' && (
              <Card>
                <CardHeader>
                  <CardTitle>Submitted Report</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div>
                      <h4 className="font-medium mb-2">{finalReport?.title}</h4>
                      <div className="p-4 bg-muted rounded-lg">
                        <p className="text-sm whitespace-pre-wrap">{finalReport?.content}</p>
                      </div>
                    </div>
                    
                    {finalReport?.supervisor_comments && (
                      <div>
                        <h4 className="font-medium mb-2">Supervisor Feedback</h4>
                        <div className="p-4 bg-blue-50 rounded-lg">
                          <p className="text-sm">{finalReport.supervisor_comments}</p>
                          {finalReport.supervisor_rating && (
                            <div className="mt-2">
                              <span className="text-sm font-medium">Rating: {finalReport.supervisor_rating}/5</span>
                            </div>
                          )}
                        </div>
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>
            )}

          </div>
        </div>
      </div>
    </div>
  )
}
