import {
    InputOTP,
    InputOTPGroup,
    InputOTPSeparator,
    InputOTPSlot,
} from "@/components/ui/input-otp";

interface InputOTPDemoProps {
    setPassword: (password: string) => void;
}

export function OTP_Input({ setPassword }: InputOTPDemoProps) {
    const handleChange = (value: string) => {
        setPassword(value);
    };

    return (
        <InputOTP maxLength={6} onChange={handleChange}>
            <InputOTPGroup>
                <InputOTPSlot index={0} />
                <InputOTPSlot index={1} />
                <InputOTPSlot index={2} />
            </InputOTPGroup>
            <InputOTPSeparator />
            <InputOTPGroup>
                <InputOTPSlot index={3} />
                <InputOTPSlot index={4} />
                <InputOTPSlot index={5} />
            </InputOTPGroup>
        </InputOTP>
    );
}