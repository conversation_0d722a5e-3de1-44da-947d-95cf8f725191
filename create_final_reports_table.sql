-- Simple SQL to create final_reports table
-- You can copy and paste this into your MySQL client

USE iptms_v2;

DROP TABLE IF EXISTS final_reports;

CREATE TABLE final_reports (
  id int NOT NULL AUTO_INCREMENT,
  student_id int NOT NULL,
  title varchar(255) NOT NULL DEFAULT 'Final Internship Report',
  submission_type enum('write','upload') NOT NULL DEFAULT 'write',
  content longtext,
  content_html longtext,
  content_plain longtext,
  executive_summary text,
  executive_summary_html text,
  objectives_achieved text,
  objectives_achieved_html text,
  challenges_faced text,
  challenges_faced_html text,
  skills_acquired text,
  skills_acquired_html text,
  recommendations text,
  recommendations_html text,
  conclusion text,
  conclusion_html text,
  file_url varchar(500) DEFAULT NULL,
  file_name varchar(255) DEFAULT NULL,
  file_size bigint DEFAULT NULL,
  file_type varchar(100) DEFAULT NULL,
  character_count int DEFAULT NULL,
  word_count int DEFAULT NULL,
  status enum('draft','submitted','reviewed','approved','needs_revision') NOT NULL DEFAULT 'draft',
  is_submitted tinyint(1) DEFAULT '0',
  submitted_at timestamp NULL DEFAULT NULL,
  reviewed_at timestamp NULL DEFAULT NULL,
  supervisor_id int DEFAULT NULL,
  supervisor_comments text,
  supervisor_rating int DEFAULT NULL,
  grade varchar(10) DEFAULT NULL,
  created_at timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  updated_at timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (id),
  UNIQUE KEY unique_student_final_report (student_id),
  KEY idx_final_reports_student_id (student_id),
  KEY idx_final_reports_status (status),
  KEY idx_final_reports_submitted (is_submitted),
  KEY idx_final_reports_supervisor_id (supervisor_id),
  KEY idx_final_reports_supervisor_rating (supervisor_rating),
  CONSTRAINT final_reports_chk_1 CHECK ((supervisor_rating >= 1) and (supervisor_rating <= 5))
) ENGINE=MyISAM DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

SELECT 'final_reports table created successfully' as status;
