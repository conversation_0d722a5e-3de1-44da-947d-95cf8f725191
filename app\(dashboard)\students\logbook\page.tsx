"use client"

import { useState, useEffect } from "react"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { DailyLogForm } from "@/features/logbook/components/daily-log-form"
import { IPTLogbooks } from "@/features/logbook/server/IPTLogbooks"
import { DailyLog, WeeklyReport, LogbookWeek } from "@/features/logbook/types/logbook"
import { useGlobalManager } from "@/hooks/use-context"
import { toast } from "sonner"
import {
  Plus,
  BookOpen,
  Calendar,
  Clock,
  FileText,
  CheckCircle,
  AlertCircle,
  Edit,
  Trash2,
  Eye,
  User,
  BarChart3
} from "lucide-react"

export default function StudentLogbookPage() {
  const { user } = useGlobalManager()
  const [currentWeek, setCurrentWeek] = useState(1)
  const [dailyLogs, setDailyLogs] = useState<DailyLog[]>([])
  const [weeklyReport, setWeeklyReport] = useState<WeeklyReport | null>(null)
  const [editingDailyLog, setEditingDailyLog] = useState<DailyLog | null>(null)
  const [showDailyLogForm, setShowDailyLogForm] = useState(false)
  const [loading, setLoading] = useState(true)
  const [saving, setSaving] = useState(false)

  useEffect(() => {
    loadWeekData()
  }, [currentWeek])

  const loadWeekData = async () => {
    try {
      setLoading(true)

      // Load daily logs for current week
      const dailyLogsResult = await IPTLogbooks.getWeekDailyLogs(currentWeek)
      setDailyLogs(dailyLogsResult?.data || [])

      // Load weekly report for current week
      const weeklyReportResult = await IPTLogbooks.getWeeklyReport(currentWeek)
      setWeeklyReport(weeklyReportResult?.data || null)

    } catch (error) {
      console.error("Failed to load week data:", error)
    } finally {
      setLoading(false)
    }
  }

  const handleSaveDailyLog = async (dailyLog: DailyLog) => {
    try {
      setSaving(true)
      if (dailyLog.id) {
        await IPTLogbooks.updateDailyLog(dailyLog.id, dailyLog)
      } else {
        await IPTLogbooks.createDailyLog(dailyLog)
      }
      await loadWeekData()
      setShowDailyLogForm(false)
      setEditingDailyLog(null)
    } catch (error) {
      console.error("Failed to save daily log:", error)
    } finally {
      setSaving(false)
    }
  }

  const handleEditDailyLog = (dailyLog: DailyLog) => {
    setEditingDailyLog(dailyLog)
    setShowDailyLogForm(true)
  }

  const handleDeleteDailyLog = async (id: string) => {
    try {
      await IPTLogbooks.deleteDailyLog(id)
      await loadWeekData()
      toast.success("Daily log deleted successfully")
    } catch (error) {
      console.error("Failed to delete daily log:", error)
    }
  }

  const handleNewDailyLog = () => {
    setEditingDailyLog(null)
    setShowDailyLogForm(true)
  }

  const handleCancelDailyLogForm = () => {
    setShowDailyLogForm(false)
    setEditingDailyLog(null)
  }

  const getWeekProgress = () => {
    const totalDays = 6 // Monday to Saturday
    const completedDays = dailyLogs.filter(log => log.is_submitted).length
    return {
      completed: completedDays,
      total: totalDays,
      percentage: Math.round((completedDays / totalDays) * 100)
    }
  }

  if (showDailyLogForm) {
    return (
      <div className="flex flex-1 flex-col">
        <div className="@container/main flex flex-1 flex-col gap-2">
          <div className="flex flex-col gap-4 py-4 md:gap-6 md:py-6">
            <div className="flex flex-col gap-4 px-4 lg:px-6">
              <div className="flex items-center justify-between">
                <div className="flex flex-col gap-2">
                  <h1 className="text-2xl font-bold">
                    {editingDailyLog ? 'Edit Daily Log' : 'New Daily Log'}
                  </h1>
                  <p className="text-muted-foreground">
                    {editingDailyLog
                      ? 'Update your daily log entry'
                      : 'Record your daily activities and progress'
                    }
                  </p>
                </div>
                <Button variant="outline" onClick={handleCancelDailyLogForm}>
                  Back to Week View
                </Button>
              </div>

              <DailyLogForm
                initialData={editingDailyLog || undefined}
                weekNumber={currentWeek}
                onSave={handleSaveDailyLog}
                onCancel={handleCancelDailyLogForm}
                loading={saving}
              />
            </div>
          </div>
        </div>
      </div>
    )
  }

  const weekProgress = getWeekProgress()

  return (
    <div className="flex flex-1 flex-col">
      <div className="@container/main flex flex-1 flex-col gap-2">
        <div className="flex flex-col gap-4 py-4 md:gap-6 md:py-6">
          <div className="flex flex-col gap-4 px-4 lg:px-6">
            <div className="flex items-center justify-between">
              <div className="flex flex-col gap-2">
                <h1 className="text-2xl font-bold">IPT Logbook</h1>
                <p className="text-muted-foreground">
                  Track your daily activities and weekly progress (8-10 weeks)
                </p>
              </div>
              <div className="flex items-center gap-2">
                <Select value={currentWeek.toString()} onValueChange={(value) => setCurrentWeek(parseInt(value))}>
                  <SelectTrigger className="w-32">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {Array.from({ length: 10 }, (_, i) => i + 1).map(week => (
                      <SelectItem key={week} value={week.toString()}>
                        Week {week}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <Button onClick={handleNewDailyLog} className="flex items-center gap-2">
                  <Plus className="h-4 w-4" />
                  Add Daily Log
                </Button>
              </div>
            </div>

            {/* Week Progress */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Calendar className="h-5 w-5" />
                  Week {currentWeek} Progress
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex items-center justify-between mb-4">
                  <div className="flex items-center gap-4">
                    <div className="text-2xl font-bold">{weekProgress.completed}/{weekProgress.total}</div>
                    <div className="text-sm text-muted-foreground">days completed</div>
                  </div>
                  <div className="flex items-center gap-2">
                    <div className="text-sm font-medium">{weekProgress.percentage}%</div>
                    {weekProgress.percentage === 100 ? (
                      <CheckCircle className="h-5 w-5 text-green-500" />
                    ) : (
                      <AlertCircle className="h-5 w-5 text-orange-500" />
                    )}
                  </div>
                </div>
                <div className="w-full bg-muted rounded-full h-2">
                  <div
                    className="bg-primary h-2 rounded-full transition-all duration-300"
                    style={{ width: `${weekProgress.percentage}%` }}
                  />
                </div>
              </CardContent>
            </Card>

            {/* Daily Logs */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <FileText className="h-5 w-5" />
                  Daily Logs
                </CardTitle>
              </CardHeader>
              <CardContent>
                {loading ? (
                  <div className="flex items-center justify-center h-32">
                    <div className="text-center">
                      <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
                      <p className="text-muted-foreground">Loading daily logs...</p>
                    </div>
                  </div>
                ) : dailyLogs.length === 0 ? (
                  <div className="text-center py-8">
                    <BookOpen className="h-12 w-12 mx-auto mb-4 text-muted-foreground opacity-50" />
                    <h3 className="text-lg font-medium mb-2">No daily logs for Week {currentWeek}</h3>
                    <p className="text-muted-foreground mb-4">Start by adding your first daily log entry</p>
                    <Button onClick={handleNewDailyLog}>
                      <Plus className="h-4 w-4 mr-2" />
                      Add Daily Log
                    </Button>
                  </div>
                ) : (
                  <div className="space-y-4">
                    {dailyLogs.map((log) => (
                      <div key={log.id} className="border rounded-lg p-4 hover:shadow-md transition-shadow">
                        <div className="flex items-start justify-between">
                          <div className="flex-1">
                            <div className="flex items-center gap-2 mb-2">
                              <Badge variant="outline">
                                {IPTLogbooks.getDayName(log.day_of_week)}
                              </Badge>
                              <span className="text-sm text-muted-foreground">{log.date}</span>
                              {log.hours_worked && (
                                <span className="text-sm text-muted-foreground flex items-center gap-1">
                                  <Clock className="h-3 w-3" />
                                  {log.hours_worked}h
                                </span>
                              )}
                              {log.is_submitted ? (
                                <Badge variant="default">Submitted</Badge>
                              ) : (
                                <Badge variant="secondary">Draft</Badge>
                              )}
                            </div>
                            <h4 className="font-medium mb-1">{log.title}</h4>
                            <p className="text-sm text-muted-foreground line-clamp-2">
                              {log.activities.substring(0, 150)}...
                            </p>
                            {log.supervisor_feedback && (
                              <div className="mt-2 p-2 bg-blue-50 rounded text-sm">
                                <strong>Supervisor:</strong> {log.supervisor_feedback}
                              </div>
                            )}
                          </div>
                          <div className="flex items-center gap-2 ml-4">
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => handleEditDailyLog(log)}
                              className="h-8 w-8 p-0"
                            >
                              <Edit className="h-4 w-4" />
                            </Button>
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => handleDeleteDailyLog(log.id!)}
                              className="h-8 w-8 p-0 text-destructive hover:text-destructive"
                            >
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Weekly Report Section */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <BarChart3 className="h-5 w-5" />
                  Weekly Report
                </CardTitle>
              </CardHeader>
              <CardContent>
                {weeklyReport ? (
                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <div>
                        <h4 className="font-medium">Week {currentWeek} Report</h4>
                        <p className="text-sm text-muted-foreground">
                          {weeklyReport.is_submitted ? 'Submitted' : 'Draft'} •
                          {weeklyReport.total_hours}h total
                        </p>
                      </div>
                      <Button variant="outline" size="sm">
                        <Edit className="h-4 w-4 mr-2" />
                        Edit Report
                      </Button>
                    </div>
                    <div className="p-4 bg-muted rounded-lg">
                      <p className="text-sm">{weeklyReport.summary}</p>
                    </div>
                  </div>
                ) : (
                  <div className="text-center py-6">
                    <FileText className="h-8 w-8 mx-auto mb-2 text-muted-foreground opacity-50" />
                    <p className="text-muted-foreground mb-4">No weekly report for Week {currentWeek}</p>
                    <Button variant="outline">
                      <Plus className="h-4 w-4 mr-2" />
                      Create Weekly Report
                    </Button>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  )
}
