"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { DailyLogForm } from "@/features/logbook/components/daily-log-form"
import { DailyLogTable } from "@/features/logbook/components/daily-log-table"
import { WeeklyReportSection } from "@/features/logbook/components/weekly-report-section"
import { IPTLogbooks } from "@/features/logbook/server/IPTLogbooks"
import { DailyLog, WeeklyReport, LogbookWeek } from "@/features/logbook/types/logbook"
import { useGlobalManager } from "@/hooks/use-context"
import { toast } from "sonner"
import {
  Calendar,
  CheckCircle,
  AlertCircle
} from "lucide-react"

export default function StudentLogbookPage() {
  const { user } = useGlobalManager()
  const [currentWeek, setCurrentWeek] = useState(1)
  const [dailyLogs, setDailyLogs] = useState<DailyLog[]>([])
  const [weeklyReport, setWeeklyReport] = useState<WeeklyReport | null>(null)

  const [loading, setLoading] = useState(true)
  const [saving, setSaving] = useState(false)

  useEffect(() => {
    loadWeekData()
  }, [currentWeek])

  const loadWeekData = async () => {
    try {
      setLoading(true)

      // Load daily logs for current week
      const dailyLogsResult = await IPTLogbooks.getWeekDailyLogs(currentWeek)
      setDailyLogs(dailyLogsResult?.data || [])

      // Load weekly report for current week
      const weeklyReportResult = await IPTLogbooks.getWeeklyReport(currentWeek)
      setWeeklyReport(weeklyReportResult?.data || null)

    } catch (error) {
      console.error("Failed to load week data:", error)
    } finally {
      setLoading(false)
    }
  }

  const handleSaveDailyLog = async (dailyLog: DailyLog) => {
    try {
      setSaving(true)
      if (dailyLog.id) {
        await IPTLogbooks.updateDailyLog(dailyLog.id, dailyLog)
      } else {
        await IPTLogbooks.createDailyLog(dailyLog)
      }
      await loadWeekData()
    } catch (error) {
      console.error("Failed to save daily log:", error)
    } finally {
      setSaving(false)
    }
  }

  const handleSaveWeeklyReport = async (weeklyReport: WeeklyReport) => {
    try {
      setSaving(true)
      if (weeklyReport.id) {
        await IPTLogbooks.updateWeeklyReport(weeklyReport.id, weeklyReport)
      } else {
        await IPTLogbooks.createWeeklyReport(weeklyReport)
      }
      await loadWeekData()
    } catch (error) {
      console.error("Failed to save weekly report:", error)
    } finally {
      setSaving(false)
    }
  }

  const handleDeleteDailyLog = async (id: string) => {
    try {
      await IPTLogbooks.deleteDailyLog(id)
      await loadWeekData()
      toast.success("Daily log deleted successfully")
    } catch (error) {
      console.error("Failed to delete daily log:", error)
    }
  }

  const getWeekProgress = () => {
    const totalDays = 6 // Monday to Saturday
    const completedDays = dailyLogs.filter(log => log.is_submitted).length
    return {
      completed: completedDays,
      total: totalDays,
      percentage: Math.round((completedDays / totalDays) * 100)
    }
  }



  const weekProgress = getWeekProgress()

  return (
    <div className="flex flex-1 flex-col">
      <div className="@container/main flex flex-1 flex-col gap-2">
        <div className="flex flex-col gap-4 py-4 md:gap-6 md:py-6">
          <div className="flex flex-col gap-4 px-4 lg:px-6">
            <div className="flex items-center justify-between">
              <div className="flex flex-col gap-2">
                <h1 className="text-2xl font-bold">IPT Logbook</h1>
                <p className="text-muted-foreground">
                  Track your daily activities and weekly progress (8-10 weeks)
                </p>
              </div>
              <div className="flex items-center gap-2">
                <Select value={currentWeek.toString()} onValueChange={(value) => setCurrentWeek(parseInt(value))}>
                  <SelectTrigger className="w-40">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {Array.from({ length: 10 }, (_, i) => i + 1).map(week => (
                      <SelectItem key={week} value={week.toString()}>
                        Week {week}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>

            {/* Week Progress */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Calendar className="h-5 w-5" />
                  Week {currentWeek} Progress
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex items-center justify-between mb-4">
                  <div className="flex items-center gap-4">
                    <div className="text-2xl font-bold">{weekProgress.completed}/{weekProgress.total}</div>
                    <div className="text-sm text-muted-foreground">days completed</div>
                  </div>
                  <div className="flex items-center gap-2">
                    <div className="text-sm font-medium">{weekProgress.percentage}%</div>
                    {weekProgress.percentage === 100 ? (
                      <CheckCircle className="h-5 w-5 text-green-500" />
                    ) : (
                      <AlertCircle className="h-5 w-5 text-orange-500" />
                    )}
                  </div>
                </div>
                <div className="w-full bg-muted rounded-full h-2">
                  <div
                    className="bg-primary h-2 rounded-full transition-all duration-300"
                    style={{ width: `${weekProgress.percentage}%` }}
                  />
                </div>
              </CardContent>
            </Card>

            {/* Daily Logs Table */}
            <DailyLogTable
              weekNumber={currentWeek}
              dailyLogs={dailyLogs}
              onSaveDailyLog={handleSaveDailyLog}
              onDeleteDailyLog={handleDeleteDailyLog}
              loading={loading}
            />

            {/* Weekly Report Section */}
            <WeeklyReportSection
              weekNumber={currentWeek}
              weeklyReport={weeklyReport}
              onSaveWeeklyReport={handleSaveWeeklyReport}
              loading={loading}
            />
          </div>
        </div>
      </div>
    </div>
  )
}
