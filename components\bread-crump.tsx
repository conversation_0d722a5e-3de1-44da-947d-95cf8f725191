import React from 'react'
import {
  Bread<PERSON>rumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb"
import { usePathname } from 'next/navigation'
import { join } from 'path';

const B_Crump = () => {
  const pathname = usePathname();
  const path = pathname.split('/');


  return (
    <Breadcrumb>
    <BreadcrumbList>
        <BreadcrumbItem className="hidden md:block">
        <BreadcrumbLink href="#">{path[1] == '' ? 'Dashboard' : path[1]}</BreadcrumbLink>
        </BreadcrumbItem>
        {path[2] && (
            <>
                <BreadcrumbSeparator className="hidden md:block" />
                <BreadcrumbItem>
                <BreadcrumbPage>{path[2]}</BreadcrumbPage>
                </BreadcrumbItem>
            </> 
        )}
        {path[3] && (
            <>
                <BreadcrumbSeparator className="hidden md:block" />
                <BreadcrumbItem>
                <BreadcrumbPage>{path[3]}</BreadcrumbPage>
                </BreadcrumbItem>
            </> 
        )}
    </BreadcrumbList>
    </Breadcrumb>
  )
}

export default B_Crump
