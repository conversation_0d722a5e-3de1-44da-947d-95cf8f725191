"use client"

import { useState, useEffect, useCallback } from "react"
import { FinalReport, Feedback, FeedbackCategory } from "../types/reports"
import { Reports } from "../server/Reports"
import { toast } from "sonner"

export const FEEDBACK_CATEGORIES: FeedbackCategory[] = [
  {
    value: 'general',
    label: 'General Experience',
    description: 'Overall internship experience and satisfaction'
  },
  {
    value: 'program',
    label: 'Program Structure',
    description: 'Feedback about the internship program design and content'
  },
  {
    value: 'supervision',
    label: 'Supervision & Mentoring',
    description: 'Quality of supervision and mentoring received'
  },
  {
    value: 'facilities',
    label: 'Facilities & Resources',
    description: 'Workplace facilities, tools, and resources provided'
  },
  {
    value: 'recommendations',
    label: 'Recommendations',
    description: 'Suggestions for program improvement'
  }
]

export function useFinalReport() {
  const [finalReport, setFinalReport] = useState<FinalReport | null>(null)
  const [loading, setLoading] = useState(true)
  const [saving, setSaving] = useState(false)

  const loadFinalReport = useCallback(async () => {
    setLoading(true)
    try {
      const result = await Reports.getFinalReport()
      setFinalReport(result?.data || {
        submission_type: 'write',
        content: '',
        is_submitted: false
      })
    } catch (error) {
      console.error("Failed to load final report:", error)
      setFinalReport({
        submission_type: 'write',
        content: '',
        is_submitted: false
      })
    } finally {
      setLoading(false)
    }
  }, [])

  const saveFinalReport = useCallback(async (reportData: FinalReport) => {
    setSaving(true)
    try {
      const result = await Reports.saveFinalReport(reportData)
      if (result?.data) {
        setFinalReport(result.data)
        return result.data
      }
    } catch (error) {
      console.error("Failed to save final report:", error)
      throw error
    } finally {
      setSaving(false)
    }
  }, [])

  const submitFinalReport = useCallback(async (reportData: FinalReport) => {
    setSaving(true)
    try {
      const result = await Reports.saveFinalReport({ ...reportData, is_submitted: true })
      if (result?.data) {
        setFinalReport(result.data)
        toast.success("Final report submitted successfully")
        return result.data
      }
    } catch (error) {
      console.error("Failed to submit final report:", error)
      throw error
    } finally {
      setSaving(false)
    }
  }, [])

  useEffect(() => {
    loadFinalReport()
  }, [loadFinalReport])

  return {
    finalReport,
    loading,
    saving,
    saveFinalReport,
    submitFinalReport,
    loadFinalReport
  }
}

export function useFeedback() {
  const [feedbackList, setFeedbackList] = useState<Feedback[]>([])
  const [loading, setLoading] = useState(true)
  const [saving, setSaving] = useState(false)

  const loadFeedback = useCallback(async () => {
    setLoading(true)
    try {
      const result = await Reports.getFeedback()
      setFeedbackList(result?.data || [])
    } catch (error) {
      console.error("Failed to load feedback:", error)
      setFeedbackList([])
    } finally {
      setLoading(false)
    }
  }, [])

  const saveFeedback = useCallback(async (feedbackData: Feedback) => {
    setSaving(true)
    try {
      const result = await Reports.saveFeedback(feedbackData)
      if (result?.data) {
        setFeedbackList(prev => {
          const existingIndex = prev.findIndex(f => f.id === result.data.id)
          if (existingIndex >= 0) {
            const updated = [...prev]
            updated[existingIndex] = result.data
            return updated
          } else {
            return [...prev, result.data]
          }
        })
        return result.data
      }
    } catch (error) {
      console.error("Failed to save feedback:", error)
      throw error
    } finally {
      setSaving(false)
    }
  }, [])

  const submitFeedback = useCallback(async (feedbackData: Feedback) => {
    setSaving(true)
    try {
      const result = await Reports.saveFeedback({ ...feedbackData, is_submitted: true })
      if (result?.data) {
        setFeedbackList(prev => {
          const existingIndex = prev.findIndex(f => f.id === result.data.id)
          if (existingIndex >= 0) {
            const updated = [...prev]
            updated[existingIndex] = result.data
            return updated
          } else {
            return [...prev, result.data]
          }
        })
        toast.success("Feedback submitted successfully")
        return result.data
      }
    } catch (error) {
      console.error("Failed to submit feedback:", error)
      throw error
    } finally {
      setSaving(false)
    }
  }, [])

  const deleteFeedback = useCallback(async (id: string) => {
    setSaving(true)
    try {
      await Reports.deleteFeedback(id)
      setFeedbackList(prev => prev.filter(f => f.id !== id))
    } catch (error) {
      console.error("Failed to delete feedback:", error)
      throw error
    } finally {
      setSaving(false)
    }
  }, [])

  useEffect(() => {
    loadFeedback()
  }, [loadFeedback])

  return {
    feedbackList,
    loading,
    saving,
    saveFeedback,
    submitFeedback,
    deleteFeedback,
    loadFeedback,
    categories: FEEDBACK_CATEGORIES
  }
}
