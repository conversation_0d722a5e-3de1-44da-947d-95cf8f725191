"use client"

import { useState, useEffect } from "react"
import { useForm } from "react-hook-form"
import { 
  <PERSON><PERSON>, 
  <PERSON><PERSON><PERSON>ontent, 
  <PERSON><PERSON><PERSON>eader, 
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>Footer
} from "@/components/ui/dialog"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from "@/components/ui/select"
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Feedback, FeedbackCategory, FileUpload } from "../types/reports"
import { RichTextEditor } from "@/features/logbook/components/rich-text-editor"
import { FileUploadComponent } from "@/features/logbook/components/file-upload"
import { ConfirmationDialog } from "@/features/logbook/components/confirmation-dialog"
import { toast } from "sonner"
import { <PERSON>, Send, Star } from "lucide-react"
import { useGlobalManager } from "@/hooks/use-context"

interface FeedbackFormProps {
  isOpen: boolean
  onClose: () => void
  onSave: (data: Feedback) => void
  onSubmit: (data: Feedback) => void
  initialData?: Partial<Feedback>
  categories: FeedbackCategory[]
  disabled?: boolean
}

type FormData = {
  category: string
  title: string
  rating: number
}

export function FeedbackForm({
  isOpen,
  onClose,
  onSave,
  onSubmit,
  initialData,
  categories,
  disabled = false
}: FeedbackFormProps) {
  const {user} = useGlobalManager();
  const [submissionType, setSubmissionType] = useState<'write' | 'upload'>(
    initialData?.submission_type || 'write'
  )
  const [content, setContent] = useState(initialData?.content || "")
  const [uploadedFile, setUploadedFile] = useState<FileUpload | undefined>()
  const [showSubmitDialog, setShowSubmitDialog] = useState(false)

  const {
    register,
    handleSubmit,
    formState: { errors },
    setValue,
    watch,
    reset
  } = useForm<FormData>({
    defaultValues: {
      category: initialData?.category || "",
      title: initialData?.title || "",
      rating: initialData?.rating || 5
    }
  })

  const watchedCategory = watch("category")
  const watchedRating = watch("rating")

  // Reset form when dialog opens/closes
  useEffect(() => {
    if (isOpen) {
      reset({
        category: initialData?.category || "",
        title: initialData?.title || "",
        rating: initialData?.rating || 5
      })
      setContent(initialData?.content || "")
      setSubmissionType(initialData?.submission_type || 'write')
      
      // Handle existing file
      if (initialData?.file_url && initialData?.file_name) {
        setUploadedFile({
          file: new File([], initialData.file_name, { type: initialData.file_type || 'application/octet-stream' }),
          preview: initialData.file_url,
          progress: 100 // Mark as fully uploaded
        })
      } else {
        setUploadedFile(undefined)
      }
    }
  }, [isOpen, initialData, reset])

  const validateFeedback = (showToast: boolean = true) => {
    if (!watchedCategory) {
      if (showToast) toast.error("Please select a feedback category")
      return false
    }
    
    if (submissionType === 'write') {
      const textContent = content.replace(/<[^>]*>/g, '').trim()
      if (textContent.length < 50) {
        if (showToast) toast.error("Feedback must be at least 50 characters long")
        return false
      }
    } else if (submissionType === 'upload') {
      if (!uploadedFile) {
        if (showToast) toast.error("Please upload a file for your feedback")
        return false
      }
    }
    return true
  }

  const handleSaveFeedback = (formData: FormData) => {
    if (!validateFeedback()) return

    const feedbackData: Feedback = {
      ...initialData,
      category: formData.category as any,
      title: formData.title,
      rating: formData.rating,
      submission_type: submissionType,
      content: submissionType === 'write' ? content : undefined,
      file_url: submissionType === 'upload' ? (uploadedFile?.preview || initialData?.file_url) : undefined,
      file_name: submissionType === 'upload' ? (uploadedFile?.file.name || initialData?.file_name) : undefined,
      file_size: submissionType === 'upload' ? (uploadedFile?.file.size || initialData?.file_size) : undefined,
      file_type: submissionType === 'upload' ? (uploadedFile?.file.type || initialData?.file_type) : undefined,
      character_count: submissionType === 'write' ? content.replace(/<[^>]*>/g, '').length : undefined,
      is_submitted: false,
      student_id: user.id
    }

    onSave(feedbackData)
  }

  const handleSubmitFeedback = (formData: FormData) => {
    if (!validateFeedback()) return

    const feedbackData: Feedback = {
      ...initialData,
      category: formData.category as any,
      title: formData.title,
      rating: formData.rating,
      submission_type: submissionType,
      content: submissionType === 'write' ? content : undefined,
      file_url: submissionType === 'upload' ? (uploadedFile?.preview || initialData?.file_url) : undefined,
      file_name: submissionType === 'upload' ? (uploadedFile?.file.name || initialData?.file_name) : undefined,
      file_size: submissionType === 'upload' ? (uploadedFile?.file.size || initialData?.file_size) : undefined,
      file_type: submissionType === 'upload' ? (uploadedFile?.file.type || initialData?.file_type) : undefined,
      character_count: submissionType === 'write' ? content.replace(/<[^>]*>/g, '').length : undefined,
      is_submitted: true,
      student_id: user.id
    }
    
    onSubmit(feedbackData)
  }

  const handleFileSelect = (file: FileUpload) => {
    setUploadedFile(file)
  }

  const handleFileRemove = () => {
    setUploadedFile(undefined)
  }

  const selectedCategory = categories.find(c => c.value === watchedCategory)

  return (
    <>
      <Dialog open={isOpen} onOpenChange={onClose}>
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>
              {initialData?.id ? "Edit Feedback" : "Add Feedback"}
            </DialogTitle>
          </DialogHeader>
          
          <form onSubmit={handleSubmit(handleSaveFeedback)}>
            <div className="grid gap-6 py-4">
              {/* Category and Title */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="category">Category *</Label>
                  <Select 
                    value={watchedCategory} 
                    onValueChange={(value) => setValue("category", value)}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select category" />
                    </SelectTrigger>
                    <SelectContent>
                      {categories.map((category) => (
                        <SelectItem key={category.value} value={category.value}>
                          {category.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  {errors.category && (
                    <p className="text-sm text-red-500">{errors.category.message}</p>
                  )}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="title">Title *</Label>
                  <Input
                    id="title"
                    {...register("title", { required: "Title is required" })}
                    placeholder="Brief title for your feedback"
                  />
                  {errors.title && (
                    <p className="text-sm text-red-500">{errors.title.message}</p>
                  )}
                </div>
              </div>

              {/* Category Description */}
              {selectedCategory && (
                <div className="p-3 bg-muted rounded-lg">
                  <p className="text-sm text-muted-foreground">{selectedCategory.description}</p>
                </div>
              )}

              {/* Rating */}
              <div className="space-y-2">
                <Label htmlFor="rating">Overall Rating</Label>
                <div className="flex items-center space-x-2">
                  {[1, 2, 3, 4, 5].map((star) => (
                    <button
                      key={star}
                      type="button"
                      onClick={() => setValue("rating", star)}
                      className={`p-1 ${star <= watchedRating ? 'text-yellow-500' : 'text-gray-300'}`}
                    >
                      <Star className="h-6 w-6 fill-current" />
                    </button>
                  ))}
                  <span className="ml-2 text-sm text-muted-foreground">
                    {watchedRating}/5 stars
                  </span>
                </div>
              </div>

              {/* Submission Type Tabs */}
              <Tabs 
                value={submissionType} 
                onValueChange={(value) => setSubmissionType(value as 'write' | 'upload')}
                className="w-full"
              >
                <TabsList className="grid w-full grid-cols-2">
                  <TabsTrigger value="write">Write Feedback</TabsTrigger>
                  {/* <TabsTrigger value="upload">Upload File</TabsTrigger> */}
                </TabsList>
                
                <TabsContent value="write" className="mt-4">
                  <RichTextEditor
                    value={content}
                    onChange={setContent}
                    placeholder="Share your detailed feedback and recommendations..."
                    minHeight="300px"
                    maxLength={3000}
                    showCharacterCount={true}
                  />
                  <p className="text-sm text-muted-foreground mt-2">
                    Minimum 50 characters required. Be specific and constructive in your feedback.
                  </p>
                </TabsContent>
                
                <TabsContent value="upload" className="mt-4">
                  <FileUploadComponent
                    onFileSelect={handleFileSelect}
                    onFileRemove={handleFileRemove}
                    currentFile={uploadedFile}
                    maxSize={10}
                    acceptedTypes={['.pdf', '.doc', '.docx']}
                  />
                  <p className="text-sm text-muted-foreground mt-2">
                    Upload your feedback as a PDF, DOC, or DOCX file (max 10MB).
                  </p>
                </TabsContent>
              </Tabs>
            </div>
            
            <DialogFooter className="flex items-center justify-between">
              <Button type="button" variant="outline" onClick={onClose}>
                Cancel
              </Button>
              
              <div className="flex items-center space-x-2">
                <Button
                  type="submit"
                  variant="outline"
                  disabled={disabled}
                  className="flex items-center space-x-2"
                >
                  <Save className="h-4 w-4" />
                  <span>Save Draft</span>
                </Button>
                
                <Button
                  type="button"
                  onClick={handleSubmit(handleSubmitFeedback)}
                  disabled={disabled || !validateFeedback(false)}
                  className="flex items-center space-x-2"
                >
                  <Send className="h-4 w-4" />
                  <span>Submit</span>
                </Button>
              </div>
            </DialogFooter>
          </form>
        </DialogContent>
      </Dialog>

      <ConfirmationDialog
        isOpen={showSubmitDialog}
        onClose={() => setShowSubmitDialog(false)}
        onConfirm={() => handleSubmit(handleSubmitFeedback)()}
        title="Submit Feedback"
        description="Are you sure you want to submit this feedback? Once submitted, you won't be able to make changes."
        confirmText="Submit Feedback"
        cancelText="Cancel"
      />
    </>
  )
}
