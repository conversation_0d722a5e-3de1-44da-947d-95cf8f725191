import axios from "axios";


export class Student {
    public static api_url = process.env.NEXT_PUBLIC_API_URL;

    public static async index() {
        try {
          const response = await axios.get(`${this.api_url}/other/getStudents`);
    
          if (response.status === 200) {
            return response.data;
          }
        } catch (error: any) {
          throw error.response.data;
        }
      }
      
    public static async show(id: string) {
        try {
            const response = await axios.get(`${this.api_url}/other/getStudent/${id}`);
        
            if (response.status === 200) {
                return response.data;
            }
        } catch (error: any) {
            throw error.response.data;
        }
    }
}