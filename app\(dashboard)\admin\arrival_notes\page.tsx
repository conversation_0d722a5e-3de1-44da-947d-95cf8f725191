import { DottedSeparator } from '@/components/dotted-separator'
import { ArrivalNoteTable } from '@/features/arrrival_note/components/arrival-note-table'
import { Metadata } from 'next'
import React from 'react'

export const metadata: Metadata = {
  title: "Arrival Notes",
  description: "Manage students' arrival notes",
  keywords: ["arrival notes", "students", "management"],
}

const page = () => {
  return (
    <div className="flex flex-1 flex-col">
      <div className="@container/main flex flex-1 flex-col gap-2">
        <div className="flex flex-col gap-4 py-4 md:gap-6 md:py-6">
          <div className="flex flex-col gap-4 px-4 lg:px-6">
            <div className="flex flex-col gap-2">
              <h1 className="text-2xl font-bold">Arrival Notes</h1>
              <p className="text-muted-foreground">
                Here is where you can manage students' arrival notes.
              </p>
              <DottedSeparator />
            </div>

            <ArrivalNoteTable />
            
          </div>
          </div>
          </div>
      </div>
  )
}

export default page
