import { useQuery } from "@tanstack/react-query";
import { toast } from "sonner";
import { Student } from "../server/Student";


export const useFetchStudents = () => {

    const query = useQuery({
        queryKey: ["students"],
        queryFn: async () => {
            try {
                const res = await Student.index();
                if (res.success) {
                    // console.log(res.data)
                    return res.data;
                }
                return [];
            } catch (error: any) {
                toast.error(error.message);
                return [];
            }
        },
    });
    return query;
}
