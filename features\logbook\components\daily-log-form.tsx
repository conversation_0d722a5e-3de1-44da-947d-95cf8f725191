"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Checkbox } from "@/components/ui/checkbox"
import { Badge } from "@/components/ui/badge"
import { FileUploadComponent } from "./file-upload"
import { DailyLog, FileUpload } from "../types/logbook"
import { useGlobalManager } from "@/hooks/use-context"
import { 
  Save, 
  Send, 
  Calendar, 
  Clock, 
  FileText, 
  User,
  AlertTriangle
} from "lucide-react"

interface DailyLogFormProps {
  initialData?: DailyLog
  weekNumber: number
  onSave: (dailyLog: DailyLog) => void
  onCancel: () => void
  loading?: boolean
}

const DAYS_OF_WEEK = [
  { value: 'monday', label: 'Monday' },
  { value: 'tuesday', label: 'Tuesday' },
  { value: 'wednesday', label: 'Wednesday' },
  { value: 'thursday', label: 'Thursday' },
  { value: 'friday', label: 'Friday' },
  { value: 'saturday', label: 'Saturday' }
]

export function DailyLogForm({
  initialData,
  weekNumber,
  onSave,
  onCancel,
  loading = false
}: DailyLogFormProps) {
  const { user } = useGlobalManager()
  const [uploadedFile, setUploadedFile] = useState<FileUpload | undefined>()
  const [errors, setErrors] = useState<Record<string, string>>({})

  useEffect(() => {
    if (initialData?.file_url && initialData?.file_name) {
      setUploadedFile({
        file: new File([], initialData.file_name, { type: initialData.file_type || 'application/octet-stream' }),
        preview: initialData.file_url,
        progress: 100
      })
    } else {
      setUploadedFile(undefined)
    }
  }, [initialData])

  const validateForm = (formData: FormData) => {
    const newErrors: Record<string, string> = {}

    const title = formData.get('title') as string
    const activities = formData.get('activities') as string
    const dayOfWeek = formData.get('day_of_week') as string
    const date = formData.get('date') as string

    if (!title?.trim()) {
      newErrors.title = "Title is required"
    }

    if (!activities?.trim()) {
      newErrors.activities = "Activities description is required"
    }

    if (!dayOfWeek) {
      newErrors.day_of_week = "Day of week is required"
    }

    if (!date) {
      newErrors.date = "Date is required"
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>, isDraft: boolean = true) => {
    e.preventDefault()
    
    const formData = new FormData(e.currentTarget)
    
    if (!validateForm(formData)) return

    const dailyLogData: DailyLog = {
      ...initialData,
      student_id: user?.id || "",
      week_number: weekNumber,
      day_of_week: formData.get('day_of_week') as any,
      date: formData.get('date') as string,
      title: formData.get('title') as string,
      activities: formData.get('activities') as string,
      tasks_completed: formData.get('tasks_completed') as string || undefined,
      challenges_faced: formData.get('challenges_faced') as string || undefined,
      skills_learned: formData.get('skills_learned') as string || undefined,
      hours_worked: parseFloat(formData.get('hours_worked') as string) || undefined,
      supervisor_present: formData.get('supervisor_present') === 'on',
      file_url: uploadedFile?.preview || initialData?.file_url,
      file_name: uploadedFile?.file.name || initialData?.file_name,
      file_size: uploadedFile?.file.size || initialData?.file_size,
      file_type: uploadedFile?.file.type || initialData?.file_type,
      status: isDraft ? 'draft' : 'submitted',
      is_submitted: !isDraft,
      submitted_at: !isDraft ? new Date().toISOString() : undefined
    }
    
    onSave(dailyLogData)
  }

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'draft':
        return <Badge variant="secondary">Draft</Badge>
      case 'submitted':
        return <Badge variant="default">Submitted</Badge>
      default:
        return <Badge variant="secondary">{status}</Badge>
    }
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            <Calendar className="h-5 w-5" />
            {initialData?.id ? 'Edit Daily Log' : 'New Daily Log'} - Week {weekNumber}
          </CardTitle>
          {initialData?.status && getStatusBadge(initialData.status)}
        </div>
      </CardHeader>
      <CardContent>
        <form onSubmit={(e) => handleSubmit(e, true)} className="space-y-6">
          {/* Basic Information */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="space-y-2">
              <Label htmlFor="day_of_week" className="flex items-center gap-2">
                <Calendar className="h-4 w-4" />
                Day of Week
              </Label>
              <Select name="day_of_week" defaultValue={initialData?.day_of_week}>
                <SelectTrigger>
                  <SelectValue placeholder="Select day" />
                </SelectTrigger>
                <SelectContent>
                  {DAYS_OF_WEEK.map(day => (
                    <SelectItem key={day.value} value={day.value}>
                      {day.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              {errors.day_of_week && (
                <p className="text-sm text-destructive flex items-center gap-1">
                  <AlertTriangle className="h-4 w-4" />
                  {errors.day_of_week}
                </p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="date">Date</Label>
              <Input
                id="date"
                name="date"
                type="date"
                defaultValue={initialData?.date}
                required
              />
              {errors.date && (
                <p className="text-sm text-destructive flex items-center gap-1">
                  <AlertTriangle className="h-4 w-4" />
                  {errors.date}
                </p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="hours_worked" className="flex items-center gap-2">
                <Clock className="h-4 w-4" />
                Hours Worked
              </Label>
              <Input
                id="hours_worked"
                name="hours_worked"
                type="number"
                step="0.5"
                min="0"
                max="24"
                defaultValue={initialData?.hours_worked || 0}
                placeholder="8.0"
              />
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="title">Daily Summary Title</Label>
            <Input
              id="title"
              name="title"
              defaultValue={initialData?.title || ""}
              placeholder="Brief title for today's activities"
              required
            />
            {errors.title && (
              <p className="text-sm text-destructive flex items-center gap-1">
                <AlertTriangle className="h-4 w-4" />
                {errors.title}
              </p>
            )}
          </div>

          {/* Content Sections */}
          <div className="space-y-6">
            <div className="space-y-2">
              <Label htmlFor="activities" className="flex items-center gap-2">
                <FileText className="h-4 w-4" />
                Activities & Work Done (Required)
              </Label>
              <Textarea
                id="activities"
                name="activities"
                defaultValue={initialData?.activities || ""}
                placeholder="Describe what you did today, projects worked on, meetings attended, etc."
                rows={4}
                required
              />
              {errors.activities && (
                <p className="text-sm text-destructive flex items-center gap-1">
                  <AlertTriangle className="h-4 w-4" />
                  {errors.activities}
                </p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="tasks_completed">Specific Tasks Completed</Label>
              <Textarea
                id="tasks_completed"
                name="tasks_completed"
                defaultValue={initialData?.tasks_completed || ""}
                placeholder="List specific tasks or deliverables completed today"
                rows={3}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="challenges_faced">Challenges Faced</Label>
              <Textarea
                id="challenges_faced"
                name="challenges_faced"
                defaultValue={initialData?.challenges_faced || ""}
                placeholder="Any difficulties, problems, or obstacles encountered"
                rows={3}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="skills_learned">Skills Learned/Developed</Label>
              <Textarea
                id="skills_learned"
                name="skills_learned"
                defaultValue={initialData?.skills_learned || ""}
                placeholder="New skills acquired or existing skills improved"
                rows={3}
              />
            </div>

            {/* Supervisor Section */}
            <div className="space-y-4 p-4 bg-muted rounded-lg">
              <div className="flex items-center space-x-2">
                <Checkbox 
                  id="supervisor_present"
                  name="supervisor_present"
                  defaultChecked={initialData?.supervisor_present}
                />
                <Label htmlFor="supervisor_present" className="flex items-center gap-2">
                  <User className="h-4 w-4" />
                  Supervisor was present/available today
                </Label>
              </div>

              {initialData?.supervisor_feedback && (
                <div className="space-y-2">
                  <Label>Supervisor Feedback</Label>
                  <div className="p-3 bg-background rounded border">
                    <p className="text-sm">{initialData.supervisor_feedback}</p>
                  </div>
                </div>
              )}
            </div>

            {/* File Upload */}
            <div className="space-y-2">
              <Label>Supporting Documents (Optional)</Label>
              <FileUploadComponent
                onFileSelect={setUploadedFile}
                onFileRemove={() => setUploadedFile(undefined)}
                currentFile={uploadedFile}
                acceptedTypes={['.pdf', '.doc', '.docx', '.txt', '.jpg', '.jpeg', '.png']}
              />
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex items-center justify-between pt-6 border-t">
            <div className="text-sm text-muted-foreground">
              {initialData?.created_at && (
                <span>Created: {new Date(initialData.created_at).toLocaleDateString()}</span>
              )}
            </div>

            <div className="flex items-center space-x-3">
              <Button
                type="button"
                variant="outline"
                onClick={onCancel}
                disabled={loading}
              >
                Cancel
              </Button>
              
              <Button
                type="submit"
                variant="outline"
                disabled={loading}
                className="flex items-center gap-2"
              >
                <Save className="h-4 w-4" />
                Save Draft
              </Button>
              
              <Button
                type="button"
                onClick={(e) => handleSubmit(e as any, false)}
                disabled={loading || initialData?.is_submitted}
                className="flex items-center gap-2"
              >
                <Send className="h-4 w-4" />
                Submit Entry
              </Button>
            </div>
          </div>
        </form>
      </CardContent>
    </Card>
  )
}
