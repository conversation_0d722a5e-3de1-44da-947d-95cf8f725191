'use client'

import { useState, useEffect } from "react"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from "@/components/ui/select"
import { Badge } from "@/components/ui/badge"
import { 
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import { 
  Download, 
  Search, 
  Filter,
  FileText,
  Calendar,
  User,
  Upload,
  Edit3,
  Eye,
  ExternalLink
} from "lucide-react"
import { Reports } from "@/features/reports/server/Reports"
import { FinalReport, AdminReportFilters } from "@/features/reports/types/reports"
import { toast } from "sonner"

interface FinalReportWithStudent extends FinalReport {
  student_name: string
  student_email: string
  program_name?: string
  supervisor_name?: string
}

export default function AdminFinalReportsPage() {
  const [loading, setLoading] = useState(true)
  const [finalReports, setFinalReports] = useState<FinalReportWithStudent[]>([])
  const [filteredReports, setFilteredReports] = useState<FinalReportWithStudent[]>([])
  const [filters, setFilters] = useState<AdminReportFilters>({
    student_name: '',
    status: 'all',
    submission_type: 'all',
    program: '',
    supervisor: ''
  })
  const [expandedReport, setExpandedReport] = useState<string | null>(null)

  useEffect(() => {
    loadFinalReports()
  }, [])

  useEffect(() => {
    applyFilters()
  }, [finalReports, filters])

  const loadFinalReports = async () => {
    setLoading(true)
    try {
      const result = await Reports.getAllFinalReports()
      const data = result?.data || []
      setFinalReports(data)
    } catch (error) {
      console.error("Failed to load final reports:", error)
      setFinalReports([])
    } finally {
      setLoading(false)
    }
  }

  const applyFilters = () => {
    let filtered = [...finalReports]

    if (filters.student_name) {
      filtered = filtered.filter(report => 
        report.student_name.toLowerCase().includes(filters.student_name!.toLowerCase())
      )
    }

    if (filters.status && filters.status !== 'all') {
      filtered = filtered.filter(report => {
        switch (filters.status) {
          case 'completed':
            return report.is_submitted
          case 'pending':
            return !report.is_submitted && (report.content || report.file_name)
          case 'overdue':
            return !report.is_submitted
          default:
            return true
        }
      })
    }

    if (filters.submission_type && filters.submission_type !== 'all') {
      filtered = filtered.filter(report => report.submission_type === filters.submission_type)
    }

    if (filters.program) {
      filtered = filtered.filter(report => 
        report.program_name?.toLowerCase().includes(filters.program!.toLowerCase())
      )
    }

    if (filters.supervisor) {
      filtered = filtered.filter(report => 
        report.supervisor_name?.toLowerCase().includes(filters.supervisor!.toLowerCase())
      )
    }

    setFilteredReports(filtered)
  }

  const handleExport = async () => {
    try {
      await Reports.exportReports('final-reports', 'excel', filters)
    } catch (error) {
      toast.error("Failed to export final reports")
    }
  }

  const getStatusBadge = (report: FinalReportWithStudent) => {
    if (report.is_submitted) {
      return <Badge className="bg-green-100 text-green-800">Submitted</Badge>
    } else if (report.content || report.file_name) {
      return <Badge variant="outline" className="border-orange-500 text-orange-700">Draft</Badge>
    } else {
      return <Badge variant="outline" className="border-gray-500 text-gray-700">Not Started</Badge>
    }
  }

  const toggleReportExpansion = (reportId: string) => {
    setExpandedReport(expandedReport === reportId ? null : reportId)
  }

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes'
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  if (loading) {
    return (
      <div className="h-full flex-1 flex-col space-y-4 p-4 md:space-y-8 md:p-8 flex">
        <div className="flex items-center justify-center h-40">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
            <p className="text-muted-foreground">Loading final reports...</p>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="h-full flex-1 flex-col space-y-4 p-4 md:space-y-8 md:p-8 flex">
      <div className="flex items-center justify-between space-y-2">
        <div>
          <h2 className="text-2xl font-bold tracking-tight">Final Reports</h2>
          <p className="text-muted-foreground">
            Review and manage all student final report submissions
          </p>
        </div>
        
        <Button onClick={handleExport}>
          <Download className="mr-2 h-4 w-4" />
          Export Reports
        </Button>
      </div>

      {/* Statistics Cards */}
      <div className="grid gap-4 md:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Reports</CardTitle>
            <FileText className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{finalReports.length}</div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Submitted</CardTitle>
            <FileText className="h-4 w-4 text-green-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">
              {finalReports.filter(r => r.is_submitted).length}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Drafts</CardTitle>
            <Edit3 className="h-4 w-4 text-orange-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-orange-600">
              {finalReports.filter(r => !r.is_submitted && (r.content || r.file_name)).length}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Not Started</CardTitle>
            <FileText className="h-4 w-4 text-gray-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-gray-600">
              {finalReports.filter(r => !r.content && !r.file_name).length}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Filters */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Filter className="h-5 w-5" />
            <span>Filters</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
            <div className="space-y-2">
              <Label htmlFor="student-search">Student Name</Label>
              <div className="relative">
                <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                <Input
                  id="student-search"
                  placeholder="Search students..."
                  value={filters.student_name || ''}
                  onChange={(e) => setFilters(prev => ({ ...prev, student_name: e.target.value }))}
                  className="pl-8"
                />
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="status-filter">Status</Label>
              <Select 
                value={filters.status || 'all'} 
                onValueChange={(value) => setFilters(prev => ({ ...prev, status: value as any }))}
              >
                <SelectTrigger>
                  <SelectValue placeholder="All statuses" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Statuses</SelectItem>
                  <SelectItem value="completed">Submitted</SelectItem>
                  <SelectItem value="pending">Draft</SelectItem>
                  <SelectItem value="overdue">Not Started</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="type-filter">Submission Type</Label>
              <Select 
                value={filters.submission_type || 'all'} 
                onValueChange={(value) => setFilters(prev => ({ ...prev, submission_type: value as any }))}
              >
                <SelectTrigger>
                  <SelectValue placeholder="All types" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Types</SelectItem>
                  <SelectItem value="write">Written</SelectItem>
                  <SelectItem value="upload">Uploaded</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="program-filter">Program</Label>
              <Input
                id="program-filter"
                placeholder="Filter by program..."
                value={filters.program || ''}
                onChange={(e) => setFilters(prev => ({ ...prev, program: e.target.value }))}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="supervisor-filter">Supervisor</Label>
              <Input
                id="supervisor-filter"
                placeholder="Filter by supervisor..."
                value={filters.supervisor || ''}
                onChange={(e) => setFilters(prev => ({ ...prev, supervisor: e.target.value }))}
              />
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Results Summary */}
      <div className="flex items-center justify-between">
        <p className="text-sm text-muted-foreground">
          Showing {filteredReports.length} of {finalReports.length} final reports
        </p>
      </div>

      {/* Reports Table */}
      <Card>
        <CardContent className="p-0">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Student</TableHead>
                <TableHead>Program</TableHead>
                <TableHead>Supervisor</TableHead>
                <TableHead>Type</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Submitted</TableHead>
                <TableHead>Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredReports.map((report) => {
                const isExpanded = expandedReport === report.id
                
                return (
                  <>
                    <TableRow key={report.id}>
                      <TableCell>
                        <div className="flex items-center space-x-2">
                          <User className="h-4 w-4 text-muted-foreground" />
                          <div>
                            <span className="font-medium">{report.student_name}</span>
                            <p className="text-xs text-muted-foreground">{report.student_email}</p>
                          </div>
                        </div>
                      </TableCell>
                      
                      <TableCell>
                        <span className="text-sm">{report.program_name || 'N/A'}</span>
                      </TableCell>
                      
                      <TableCell>
                        <span className="text-sm">{report.supervisor_name || 'N/A'}</span>
                      </TableCell>
                      
                      <TableCell>
                        <div className="flex items-center space-x-2">
                          {report.submission_type === 'upload' ? (
                            <Upload className="h-4 w-4 text-blue-500" />
                          ) : (
                            <Edit3 className="h-4 w-4 text-green-500" />
                          )}
                          <span className="capitalize">{report.submission_type}</span>
                        </div>
                      </TableCell>
                      
                      <TableCell>
                        {getStatusBadge(report)}
                      </TableCell>
                      
                      <TableCell>
                        <div className="flex items-center space-x-2">
                          <Calendar className="h-4 w-4 text-muted-foreground" />
                          <span className="text-sm">
                            {report.submitted_at 
                              ? new Date(report.submitted_at).toLocaleDateString()
                              : 'Not submitted'
                            }
                          </span>
                        </div>
                      </TableCell>
                      
                      <TableCell>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => toggleReportExpansion(report.id!)}
                          className="h-8 w-8 p-0"
                        >
                          <Eye className="h-4 w-4" />
                        </Button>
                      </TableCell>
                    </TableRow>
                    
                    {/* Expanded Details */}
                    {isExpanded && (
                      <TableRow>
                        <TableCell colSpan={7} className="bg-muted/50">
                          <div className="p-4 space-y-4">
                            <h4 className="font-semibold">Final Report Details</h4>
                            
                            {report.submission_type === 'write' && report.content ? (
                              <div className="space-y-2">
                                <div className="flex items-center justify-between">
                                  <span className="text-sm font-medium">Written Report</span>
                                  <span className="text-xs text-muted-foreground">
                                    {report.character_count} characters
                                  </span>
                                </div>
                                <div 
                                  className="p-3 bg-background rounded border text-sm max-h-40 overflow-y-auto"
                                  dangerouslySetInnerHTML={{ 
                                    __html: report.content.length > 500 
                                      ? report.content.substring(0, 500) + '...' 
                                      : report.content 
                                  }}
                                />
                              </div>
                            ) : report.submission_type === 'upload' && report.file_name ? (
                              <div className="space-y-2">
                                <span className="text-sm font-medium">Uploaded File</span>
                                <div className="flex items-center justify-between p-3 bg-background rounded border">
                                  <div className="flex items-center space-x-2">
                                    <FileText className="h-5 w-5 text-blue-500" />
                                    <div>
                                      <p className="font-medium text-sm">{report.file_name}</p>
                                      <p className="text-xs text-muted-foreground">
                                        {report.file_type} • {report.file_size ? formatFileSize(report.file_size) : 'Unknown size'}
                                      </p>
                                    </div>
                                  </div>
                                  {report.file_url && (
                                    <Button variant="outline" size="sm" asChild>
                                      <a href={report.file_url} target="_blank" rel="noopener noreferrer">
                                        <ExternalLink className="h-4 w-4" />
                                      </a>
                                    </Button>
                                  )}
                                </div>
                              </div>
                            ) : (
                              <div className="p-3 bg-background rounded border text-center text-muted-foreground">
                                <FileText className="h-8 w-8 mx-auto mb-2 opacity-50" />
                                <p>No content available</p>
                              </div>
                            )}
                            
                            {/* Metadata */}
                            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 pt-2 border-t text-xs text-muted-foreground">
                              <div>
                                <span className="font-medium">Created:</span>
                                <p>{report.created_at ? new Date(report.created_at).toLocaleString() : 'N/A'}</p>
                              </div>
                              <div>
                                <span className="font-medium">Updated:</span>
                                <p>{report.updated_at ? new Date(report.updated_at).toLocaleString() : 'N/A'}</p>
                              </div>
                              <div>
                                <span className="font-medium">Submitted:</span>
                                <p>{report.submitted_at ? new Date(report.submitted_at).toLocaleString() : 'Not submitted'}</p>
                              </div>
                              <div>
                                <span className="font-medium">Status:</span>
                                <p className={report.is_submitted ? 'text-green-600' : 'text-orange-600'}>
                                  {report.is_submitted ? 'Final Submission' : 'Draft'}
                                </p>
                              </div>
                            </div>
                          </div>
                        </TableCell>
                      </TableRow>
                    )}
                  </>
                )
              })}
            </TableBody>
          </Table>
          
          {filteredReports.length === 0 && (
            <div className="p-8 text-center text-muted-foreground">
              <FileText className="h-12 w-12 mx-auto mb-4 opacity-50" />
              <p className="text-lg font-medium mb-2">No final reports found</p>
              <p className="text-sm">Try adjusting your filters or check back later</p>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}
