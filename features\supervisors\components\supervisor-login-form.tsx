import { cn } from "@/lib/utils";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { useGlobalManager } from "@/hooks/use-context";
import { useForm, SubmitHandler } from "react-hook-form";
import { OTP_Input } from "@/components/otp-input";
import { useEffect, useState } from "react";
import { useSupervisorLogin } from "../api/use-supervisor-login";

type FormData = {
  email: string;
  password: string;
};

interface OneTimeLoginFormProps {
    email?: string;
  }

export function OneTimeLoginForm({ email, ...props }: OneTimeLoginFormProps) {


//   const { setAuthentication } = useGlobalManager();

  const login = useSupervisorLogin();


const {
    register,
    handleSubmit,
    formState: { errors },
    setValue
} = useForm<FormData>();

useEffect(() => {
    if (email) {
      setValue("email", email);
    }
  }, [email, setValue]);

  const onSubmit: SubmitHandler<FormData> = (data) => {
    login.mutate(
      data,
      {
        onSuccess: () => {
          // setAuthentication(true); 
        },
      }
    );
  };

  return (
    <div className={cn("flex flex-col gap-6")} {...props}>
      <Card>
        <CardHeader>
          <CardTitle className="text-2xl text-center">OTP Authentication</CardTitle>
          <CardDescription>
            Enter the 6-digit code sent to your email address.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit(onSubmit)}>
            <div className="flex flex-col gap-6">
              
              <div className="grid gap-2">
               {/* <Label htmlFor="email">Enter yout OTP:</Label> */}
                <div className="flex items-center justify-center">
                    <OTP_Input setPassword={(val) => {
                        setValue("password", val);
                    }} />
                </div>
              </div>
             
              <Button type="submit" 
                      className="w-full" 
                      disabled={login.isPending}
                      >
                {login.isPending ? "Logging in..." : "Login"}
              </Button>
            </div>
            
          </form>
        </CardContent>
      </Card>
    </div>
  );
}
