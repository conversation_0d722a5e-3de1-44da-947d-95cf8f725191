"use client"

import { useState } from "react"
import { <PERSON>, Card<PERSON><PERSON>nt, Card<PERSON>eader, Card<PERSON>itle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { ConfirmationDialog } from "./confirmation-dialog"
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { Logbook } from "../types/logbook"
import { 
  Edit, 
  Trash2, 
  FileText, 
  Calendar,
  Clock,
  Eye,
  Download,
  Search,
  Filter,
  BookOpen,
  User
} from "lucide-react"

interface LogbookListProps {
  logbooks: Logbook[]
  onEdit: (logbook: Logbook) => void
  onDelete: (id: string) => void
  loading?: boolean
  showStudentInfo?: boolean
}

interface FilePreviewDialogProps {
  isOpen: boolean
  onClose: () => void
  logbook: Logbook | null
}

function FilePreviewDialog({ isOpen, onClose, logbook }: FilePreviewDialogProps) {
  if (!logbook) return null

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes'
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  const handleDownload = () => {
    if (logbook.file_url) {
      const link = document.createElement('a')
      link.href = logbook.file_url
      link.download = logbook.file_name || 'logbook-file'
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
    }
  }

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[80vh]">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <FileText className="h-5 w-5" />
            File Preview: {logbook.file_name}
          </DialogTitle>
        </DialogHeader>
        
        <div className="space-y-4">
          {/* File Info */}
          <div className="flex items-center justify-between p-4 bg-muted rounded-lg">
            <div className="space-y-1">
              <p className="font-medium">{logbook.file_name}</p>
              <p className="text-sm text-muted-foreground">
                {logbook.file_type} • {logbook.file_size ? formatFileSize(logbook.file_size) : 'Unknown size'}
              </p>
            </div>
            <Button onClick={handleDownload} variant="outline" size="sm">
              <Download className="h-4 w-4 mr-2" />
              Download
            </Button>
          </div>

          {/* File Preview */}
          <div className="border rounded-lg overflow-hidden" style={{ height: '500px' }}>
            {logbook.file_type === 'application/pdf' && logbook.file_url ? (
              <iframe
                src={logbook.file_url}
                className="w-full h-full"
                title="PDF Preview"
              />
            ) : (
              <div className="flex items-center justify-center h-full bg-muted">
                <div className="text-center">
                  <FileText className="h-16 w-16 mx-auto mb-4 text-muted-foreground" />
                  <p className="text-lg font-medium">Preview not available</p>
                  <p className="text-sm text-muted-foreground">
                    This file type cannot be previewed in the browser
                  </p>
                  <Button onClick={handleDownload} className="mt-4">
                    <Download className="h-4 w-4 mr-2" />
                    Download to view
                  </Button>
                </div>
              </div>
            )}
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
}

export function LogbookList({
  logbooks,
  onEdit,
  onDelete,
  loading = false,
  showStudentInfo = false
}: LogbookListProps) {
  const [deleteConfirm, setDeleteConfirm] = useState<string | null>(null)
  const [previewDialog, setPreviewDialog] = useState<{
    isOpen: boolean
    logbook: Logbook | null
  }>({
    isOpen: false,
    logbook: null
  })
  const [searchTerm, setSearchTerm] = useState("")
  const [statusFilter, setStatusFilter] = useState<string>("all")
  const [weekFilter, setWeekFilter] = useState<string>("all")

  const handleDelete = (id: string) => {
    setDeleteConfirm(id)
  }

  const confirmDelete = () => {
    if (deleteConfirm) {
      onDelete(deleteConfirm)
      setDeleteConfirm(null)
    }
  }

  const handlePreview = (logbook: Logbook) => {
    setPreviewDialog({
      isOpen: true,
      logbook
    })
  }

  const closePreview = () => {
    setPreviewDialog({
      isOpen: false,
      logbook: null
    })
  }

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'draft':
        return <Badge variant="secondary">Draft</Badge>
      case 'submitted':
        return <Badge variant="default">Submitted</Badge>
      case 'reviewed':
        return <Badge variant="outline">Reviewed</Badge>
      case 'approved':
        return <Badge className="bg-green-100 text-green-800">Approved</Badge>
      case 'needs_revision':
        return <Badge variant="destructive">Needs Revision</Badge>
      default:
        return <Badge variant="secondary">{status}</Badge>
    }
  }

  // Filter logbooks based on search and filters
  const filteredLogbooks = logbooks.filter(logbook => {
    const matchesSearch = logbook.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         logbook.content?.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         logbook.activities?.toLowerCase().includes(searchTerm.toLowerCase())
    
    const matchesStatus = statusFilter === "all" || logbook.status === statusFilter
    const matchesWeek = weekFilter === "all" || logbook.week_number.toString() === weekFilter

    return matchesSearch && matchesStatus && matchesWeek
  })

  if (loading) {
    return (
      <div className="space-y-4">
        {[1, 2, 3].map((i) => (
          <Card key={i} className="animate-pulse">
            <CardContent className="p-6">
              <div className="h-4 bg-muted rounded w-3/4 mb-2"></div>
              <div className="h-3 bg-muted rounded w-1/2"></div>
            </CardContent>
          </Card>
        ))}
      </div>
    )
  }

  return (
    <>
      {/* Filters */}
      <Card className="mb-6">
        <CardContent className="p-4">
          <div className="flex flex-col md:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Search logbooks..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            
            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger className="w-full md:w-40">
                <SelectValue placeholder="Status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Status</SelectItem>
                <SelectItem value="draft">Draft</SelectItem>
                <SelectItem value="submitted">Submitted</SelectItem>
                <SelectItem value="reviewed">Reviewed</SelectItem>
                <SelectItem value="approved">Approved</SelectItem>
                <SelectItem value="needs_revision">Needs Revision</SelectItem>
              </SelectContent>
            </Select>

            <Select value={weekFilter} onValueChange={setWeekFilter}>
              <SelectTrigger className="w-full md:w-32">
                <SelectValue placeholder="Week" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Weeks</SelectItem>
                {Array.from({ length: 20 }, (_, i) => i + 1).map(week => (
                  <SelectItem key={week} value={week.toString()}>
                    Week {week}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* Logbook List */}
      <div className="space-y-4">
        {filteredLogbooks.length === 0 ? (
          <Card>
            <CardContent className="p-8 text-center">
              <BookOpen className="h-12 w-12 mx-auto mb-4 text-muted-foreground" />
              <h3 className="text-lg font-medium mb-2">No logbooks found</h3>
              <p className="text-muted-foreground">
                {logbooks.length === 0 
                  ? "You haven't created any logbook entries yet."
                  : "No logbooks match your current filters."
                }
              </p>
            </CardContent>
          </Card>
        ) : (
          filteredLogbooks.map((logbook) => (
            <Card key={logbook.id} className="hover:shadow-md transition-shadow">
              <CardHeader className="pb-3">
                <div className="flex items-start justify-between">
                  <div className="space-y-1">
                    <CardTitle className="text-lg">{logbook.title}</CardTitle>
                    <div className="flex items-center gap-4 text-sm text-muted-foreground">
                      <span className="flex items-center gap-1">
                        <Calendar className="h-4 w-4" />
                        Week {logbook.week_number}
                      </span>
                      {logbook.hours_logged && (
                        <span className="flex items-center gap-1">
                          <Clock className="h-4 w-4" />
                          {logbook.hours_logged}h
                        </span>
                      )}
                      {showStudentInfo && logbook.student_name && (
                        <span className="flex items-center gap-1">
                          <User className="h-4 w-4" />
                          {logbook.student_name}
                        </span>
                      )}
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    {getStatusBadge(logbook.status)}
                  </div>
                </div>
              </CardHeader>
              
              <CardContent className="pt-0">
                {/* Content Preview */}
                {logbook.content && (
                  <div className="mb-4">
                    <p className="text-sm text-muted-foreground line-clamp-3">
                      {logbook.content.replace(/<[^>]*>/g, '').substring(0, 200)}...
                    </p>
                  </div>
                )}

                {/* File Attachment */}
                {logbook.file_name && (
                  <div className="mb-4">
                    <div className="flex items-center justify-between p-3 bg-muted rounded-lg">
                      <div className="flex items-center space-x-2">
                        <FileText className="h-5 w-5 text-blue-500" />
                        <div>
                          <p className="font-medium text-sm">{logbook.file_name}</p>
                          {logbook.file_size && (
                            <p className="text-xs text-muted-foreground">
                              {(logbook.file_size / 1024 / 1024).toFixed(2)} MB
                            </p>
                          )}
                        </div>
                      </div>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handlePreview(logbook)}
                        className="h-8 px-2"
                      >
                        <Eye className="h-4 w-4 mr-1" />
                        Preview
                      </Button>
                    </div>
                  </div>
                )}

                {/* Supervisor Feedback */}
                {logbook.supervisor_feedback && (
                  <div className="mb-4 p-3 bg-blue-50 rounded-lg">
                    <p className="text-sm font-medium text-blue-900 mb-1">Supervisor Feedback:</p>
                    <p className="text-sm text-blue-800">{logbook.supervisor_feedback}</p>
                  </div>
                )}

                {/* Actions */}
                <div className="flex items-center justify-between">
                  <div className="text-xs text-muted-foreground">
                    {logbook.created_at && (
                      <span>Created: {new Date(logbook.created_at).toLocaleDateString()}</span>
                    )}
                    {logbook.submitted_at && (
                      <span className="ml-4">
                        Submitted: {new Date(logbook.submitted_at).toLocaleDateString()}
                      </span>
                    )}
                  </div>

                  <div className="flex items-center space-x-2">
                    {logbook.file_name && (
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handlePreview(logbook)}
                        className="h-8 w-8 p-0"
                        title="Preview file"
                      >
                        <Eye className="h-4 w-4" />
                      </Button>
                    )}
                    
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => onEdit(logbook)}
                      disabled={loading}
                      className="h-8 w-8 p-0"
                      title="Edit logbook"
                    >
                      <Edit className="h-4 w-4" />
                    </Button>
                    
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleDelete(logbook.id!)}
                      disabled={loading}
                      className="h-8 w-8 p-0 text-destructive hover:text-destructive"
                      title="Delete logbook"
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))
        )}
      </div>

      <ConfirmationDialog
        isOpen={!!deleteConfirm}
        onClose={() => setDeleteConfirm(null)}
        onConfirm={confirmDelete}
        title="Delete Logbook"
        description="Are you sure you want to delete this logbook entry? This action cannot be undone."
        confirmText="Delete"
        cancelText="Cancel"
        variant="destructive"
      />

      <FilePreviewDialog
        isOpen={previewDialog.isOpen}
        onClose={closePreview}
        logbook={previewDialog.logbook}
      />
    </>
  )
}
