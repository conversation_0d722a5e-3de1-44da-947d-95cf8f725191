"use client"

import { useState, useEffect, useCallback } from "react"
import { LogDay, WeeklyReport, DayOption } from "../types/logbook"
import { Logbook } from "../server/Logbook"
import { toast } from "sonner"

const DAY_OPTIONS: DayOption[] = [
  { value: 1, label: "Monday" },
  { value: 2, label: "Tuesday" },
  { value: 3, label: "Wednesday" },
  { value: 4, label: "Thursday" },
  { value: 5, label: "Friday" },
  { value: 6, label: "Saturday" },
]

export function useLogbook(initialWeek: number = 1) {
  const [currentWeek, setCurrentWeek] = useState(initialWeek)
  const [dailyLogs, setDailyLogs] = useState<LogDay[]>([])
  const [weeklyReport, setWeeklyReport] = useState<WeeklyReport | null>(null)
  const [loading, setLoading] = useState(true)
  const [saving, setSaving] = useState(false)

  // Get available days for the current week
  const getAvailableDays = useCallback(() => {
    const existingDays = dailyLogs.map(log => log.day_number)
    return DAY_OPTIONS.map(day => ({
      ...day,
      disabled: existingDays.includes(day.value)
    }))
  }, [dailyLogs])

  // Get the next sequential day to add
  const getNextDay = useCallback(() => {
    const existingDays = dailyLogs.map(log => log.day_number).sort((a, b) => a - b)
    
    // If no days exist, start with Monday
    if (existingDays.length === 0) return 1
    
    // Find the next sequential day
    for (let i = 1; i <= 6; i++) {
      if (!existingDays.includes(i)) {
        return i
      }
    }
    
    // All days are filled
    return null
  }, [dailyLogs])

  // Check if all days are filled (6 days max)
  const isWeekFull = useCallback(() => {
    return dailyLogs.length >= 6
  }, [dailyLogs])

  // Load data for a specific week
  const loadWeekData = useCallback(async (weekNumber: number) => {
    setLoading(true)
    try {
      const [logsData, reportData] = await Promise.all([
        Logbook.getDailyLogs(weekNumber),
        Logbook.getWeeklyReport(weekNumber)
      ])
      
      setDailyLogs(logsData?.data || [])
      setWeeklyReport(reportData?.data || {
        week_number: weekNumber,
        submission_type: 'write',
        content: '',
        is_submitted: false
      })
    } catch (error) {
      console.error("Failed to load week data:", error)
      // Set default empty state
      setDailyLogs([])
      setWeeklyReport({
        week_number: weekNumber,
        submission_type: 'write',
        content: '',
        is_submitted: false
      })
    } finally {
      setLoading(false)
    }
  }, [])

  // Create a new daily log
  const createDailyLog = useCallback(async (logData: Omit<LogDay, 'id'>) => {
    setSaving(true)
    try {
      const result = await Logbook.createDailyLog(logData as LogDay)
      if (result?.data) {
        setDailyLogs(prev => [...prev, result.data])
        return result.data
      }
    } catch (error) {
      console.error("Failed to create daily log:", error)
      throw error
    } finally {
      setSaving(false)
    }
  }, [])

  // Update an existing daily log
  const updateDailyLog = useCallback(async (id: string, updates: Partial<LogDay>) => {
    setSaving(true)
    try {
      const result = await Logbook.updateDailyLog(id, updates)
      if (result?.data) {
        setDailyLogs(prev => prev.map(log => 
          log.id === id ? { ...log, ...updates } : log
        ))
        return result.data
      }
    } catch (error) {
      console.error("Failed to update daily log:", error)
      throw error
    } finally {
      setSaving(false)
    }
  }, [])

  // Save weekly report
  const saveWeeklyReport = useCallback(async (reportData: WeeklyReport) => {
    setSaving(true)
    try {
      const result = await Logbook.saveWeeklyReport(reportData)
      if (result?.data) {
        setWeeklyReport(result.data)
        return result.data
      }
    } catch (error) {
      console.error("Failed to save weekly report:", error)
      throw error
    } finally {
      setSaving(false)
    }
  }, [])

  // Change week
  const changeWeek = useCallback((weekNumber: number) => {
    if (weekNumber >= 1 && weekNumber <= 10) {
      setCurrentWeek(weekNumber)
    }
  }, [])

  // Load data when week changes
  useEffect(() => {
    loadWeekData(currentWeek)
  }, [currentWeek, loadWeekData])

  return {
    // State
    currentWeek,
    dailyLogs,
    weeklyReport,
    loading,
    saving,
    
    // Computed values
    availableDays: getAvailableDays(),
    nextDay: getNextDay(),
    isWeekFull: isWeekFull(),
    
    // Actions
    changeWeek,
    createDailyLog,
    updateDailyLog,
    saveWeeklyReport,
    loadWeekData,
    
    // Constants
    DAY_OPTIONS
  }
}
