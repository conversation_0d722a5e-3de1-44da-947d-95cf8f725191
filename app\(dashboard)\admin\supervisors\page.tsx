import { DottedSeparator } from '@/components/dotted-separator'
import { SupervisorTable } from '@/features/supervisors/components/supervisor-table'
import { Metadata } from 'next'
import React from 'react'

export const metadata: Metadata = {
  title: "Supervisors",
  description: "Manage supervisors",
  keywords: ["supervisors", "management", "admin"],
}

const page = () => {
  return (
    <div className="flex flex-1 flex-col">
      <div className="@container/main flex flex-1 flex-col gap-2">
        <div className="flex flex-col gap-4 py-4 md:gap-6 md:py-6">
          <div className="flex flex-col gap-4 px-4 lg:px-6">
            <div className="flex flex-col gap-2">
              <h1 className="text-2xl font-bold">Supervisors</h1>
              <p className="text-muted-foreground">
                Here is where you can manage all supervisors.
              </p>
              <DottedSeparator />
            </div>

            <SupervisorTable />
            
          </div>
          </div>
          </div>
      </div>
  )
}

export default page
