"use client"

import { But<PERSON> } from "@/components/ui/button"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"

interface WeekSelectorProps {
  currentWeek: number
  onWeekChange: (week: number) => void
}

export function WeekSelector({ currentWeek, onWeekChange }: WeekSelectorProps) {
  return (
    <div className="flex items-center space-x-2">
      <Button
        variant="outline"
        size="sm"
        onClick={() => onWeekChange(Math.max(1, currentWeek - 1))}
        disabled={currentWeek <= 1}
      >
        Previous
      </Button>
      
      <Select
        value={currentWeek.toString()}
        onValueChange={(value) => onWeekChange(parseInt(value))}
      >
        <SelectTrigger className="w-[180px]">
          <SelectValue placeholder="Select week" />
        </SelectTrigger>
        <SelectContent>
          {Array.from({ length: 10 }, (_, i) => i + 1).map((week) => (
            <SelectItem key={week} value={week.toString()}>
              Week {week}
            </SelectItem>
          ))}
        </SelectContent>
      </Select>
      
      <Button
        variant="outline"
        size="sm"
        onClick={() => onWeekChange(Math.min(10, currentWeek + 1))}
        disabled={currentWeek >= 10}
      >
        Next
      </Button>
    </div>
  )
}