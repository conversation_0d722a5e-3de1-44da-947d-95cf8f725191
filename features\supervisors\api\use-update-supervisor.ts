import { useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";
import { Supervisor } from "../server/Supervisor";
import { supervisor } from "@/features/auth/types/auth";
import { useGlobalManager } from "@/hooks/use-context";

export const useUpdateSupervisor = () => {
  const queryClient = useQueryClient();
  const { iptId: ipt_id } = useGlobalManager();

  const mutation = useMutation<supervisor, Error, { first_name: string , last_name: string , email: string , phone: string , id: string }>({ 
    mutationFn: async ({ first_name , last_name , email , phone , id}: { first_name: string , last_name: string , email: string , phone: string , id: string}) => {
      const res = await Supervisor.update(first_name , last_name , email , phone, ipt_id ?? "" , id);
      return res;
    },
    onSuccess: () => {

        queryClient.invalidateQueries({ queryKey: ["supervisors"] });

    },

    onError: (error) => {
      toast.error(error.message);
    },
  });

  return mutation;
};
