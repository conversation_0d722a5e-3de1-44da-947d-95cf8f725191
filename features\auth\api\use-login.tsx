import { useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";
import { Auth } from "../server/Auth";
import { useGlobalManager } from "@/hooks/use-context";

export const useLogin = () => {
  const queryClient = useQueryClient();
  const { handleLogin } = useGlobalManager();

  const mutation = useMutation<void, Error, { email: string; password: string }>({ 
    mutationFn: async ({ email, password }: { email: string; password: string }) => {
      
      const res = await Auth.login(email, password);
      return res;
    },
    onSuccess: (data) => {
        handleLogin(data);

        queryClient.invalidateQueries({ queryKey: ["auth"] });

      },
      
      onError: (error) => {
      console.log(error);
      toast.error(error.message);
    },
  });

  return mutation;
};
