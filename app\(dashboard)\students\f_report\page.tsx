'use client'

import { FinalReportEditor } from "@/features/reports/components/final-report-editor"
import { useFinalReport } from "@/features/reports/hooks/use-reports"
import { Card, CardContent } from "@/components/ui/card"
import { FileText, Clock, CheckCircle2 } from "lucide-react"

export default function FinalReportPage() {
  const {
    finalReport,
    loading,
    saving,
    saveFinalReport,
    submitFinalReport
  } = useFinalReport()

  if (loading) {
    return (
      <div className="h-full flex-1 flex-col space-y-4 p-4 md:space-y-8 md:p-8 flex">
        <div className="flex items-center justify-center h-40">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
            <p className="text-muted-foreground">Loading final report...</p>
          </div>
        </div>
      </div>
    )
  }

  const isSubmitted = finalReport?.is_submitted || false

  return (
    <div className="h-full flex-1 flex-col space-y-4 p-4 md:space-y-8 md:p-8 flex">
      <div className="flex items-center justify-between space-y-2">
        <div>
          <h2 className="text-2xl font-bold tracking-tight">Final Report</h2>
          <p className="text-muted-foreground">
            Submit your comprehensive final report for the internship program
          </p>
        </div>
      </div>

      {/* Status Card */}
      <Card className="w-full">
        <CardContent className="p-6">
          <div className="flex items-center space-x-4">
            {isSubmitted ? (
              <>
                <CheckCircle2 className="h-8 w-8 text-green-500" />
                <div>
                  <h3 className="font-semibold text-green-700">Report Submitted</h3>
                  <p className="text-sm text-muted-foreground">
                    Your final report was submitted on {finalReport?.submitted_at ? new Date(finalReport.submitted_at).toLocaleDateString() : 'Unknown date'}
                  </p>
                </div>
              </>
            ) : finalReport?.content || finalReport?.file_name ? (
              <>
                <Clock className="h-8 w-8 text-orange-500" />
                <div>
                  <h3 className="font-semibold text-orange-700">Draft Saved</h3>
                  <p className="text-sm text-muted-foreground">
                    You have a draft saved. Continue editing or submit when ready.
                  </p>
                </div>
              </>
            ) : (
              <>
                <FileText className="h-8 w-8 text-blue-500" />
                <div>
                  <h3 className="font-semibold text-blue-700">Ready to Start</h3>
                  <p className="text-sm text-muted-foreground">
                    Begin writing your final report or upload a completed document.
                  </p>
                </div>
              </>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Final Report Editor */}
      <FinalReportEditor
        initialReport={finalReport || undefined}
        onSave={saveFinalReport}
        onSubmit={submitFinalReport}
        disabled={saving}
      />
    </div>
  )
}
