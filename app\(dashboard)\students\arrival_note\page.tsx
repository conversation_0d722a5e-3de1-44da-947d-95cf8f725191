"use client"

import React from 'react'
import { <PERSON><PERSON> } from '@/components/ui/button'
import { DottedSeparator } from '@/components/dotted-separator'
import { useArrivalNoteFormModal } from '@/features/arrrival_note/hooks/use-arrival-note-form-modal'
import { useGlobalManager } from '@/hooks/use-context'
import { useRetrieveArrivalNote } from '@/features/arrrival_note/api/use-retrieve-arrival-note'
// import { Metadata } from 'next'

// export const metadata: Metadata = {
//   title: "Arrival Note",
//   description: "Submit your arrival note",
//   keywords: ["arrival note", "submit arrival note", "arrival note form"],
// }

const page = () => {
  const { hasSubmitArrivalNote , user } = useGlobalManager()
  const arrivalNote = useArrivalNoteFormModal()

  const userId = user?.id

  const { data: arrivalnote } = useRetrieveArrivalNote(userId)

  return (
    <div className="flex flex-1 flex-col">
      <div className="@container/main flex flex-1 flex-col gap-2">
        <div className="flex flex-col gap-4 py-4 md:gap-6 md:py-6">
          <div className="flex flex-col gap-4 px-4 lg:px-6">
            <div className="flex flex-col gap-2">
              <h1 className="text-2xl font-bold">Arrival Note</h1>
              <p className="text-muted-foreground">
                Here is where you can submit your arrival note.
              </p>
              <DottedSeparator />
            </div>
          </div>
          {hasSubmitArrivalNote ? (
            <div className="px-4 lg:px-6">
              <div className="rounded-lg border bg-card text-card-foreground shadow-sm p-6">
              <div className="flex flex-col gap-4">
                <div className="space-y-2">
                <h3 className="text-xl font-semibold">Your Arrival Note Details</h3>
                <p className="text-sm text-muted-foreground">Submitted arrival Note</p>
                </div>
                {arrivalnote ? (

                <div className="grid gap-4 md:grid-cols-2">
                  <div className="space-y-2">
                  <h4 className="font-medium">Organization</h4>
                  <p className="text-sm text-muted-foreground">{arrivalnote.organization}</p>
                  </div>
                  <div className="space-y-2">
                  <h4 className="font-medium">Industrial Supervisor's name </h4>
                  <p className="text-sm text-muted-foreground">{arrivalnote.i_supervisor_name}</p>
                  </div>
                  <div className="space-y-2">
                  <h4 className="font-medium">Industrial Supervisor's Phone Number </h4>
                  <p className="text-sm text-muted-foreground">{arrivalnote.i_supervisor_phone}</p>
                  </div>
                  <div className="space-y-2">
                  <h4 className="font-medium">Region</h4>
                  <p className="text-sm text-muted-foreground">{arrivalnote.region}</p>
                  </div>
                  <div className="space-y-2">
                  <h4 className="font-medium">District</h4>
                  <p className="text-sm text-muted-foreground">{arrivalnote.district}</p>
                  </div>
                  <div className="space-y-2">
                  <h4 className="font-medium">Ward</h4>
                  <p className="text-sm text-muted-foreground">{arrivalnote.ward}</p>
                  </div>
                  <div className="space-y-2">
                  <h4 className="font-medium">Street</h4>
                  <p className="text-sm text-muted-foreground">{arrivalnote.street}</p>
                  </div>
                  <div className="space-y-2">
                  <h4 className="font-medium">House Number</h4>
                  <p className="text-sm text-muted-foreground">{arrivalnote.house_number}</p>
                  </div>
                  </div>
                ):(
                  <div className="">
                    <p className="text-muted-foreground text-center">No arrival note found.</p>
                  </div>
                )}
              </div>
              </div>
            </div>
          ) : (
            <div className="flex justify-center items-center my-48">
              <div className="flex flex-col gap-4 px-4 lg:px-6">
                <div className="flex flex-col gap-4">
                  <p className="text-muted-foreground text-center">
                    Ooops... It seems that you have not submitted your arrival note yet. 
                  </p>
                  <div className="flex justify-center items-center">

                    <Button className="w-48 " 
                     onClick={arrivalNote.open}
                    >
                      Submit Arrival Note
                    </Button>
                  </div>
                </div>
                </div>
            </div>
          )}
          </div>
          </div>
      </div>
  )
}

export default page
