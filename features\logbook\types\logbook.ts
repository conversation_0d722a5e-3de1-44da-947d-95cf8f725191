export interface DailyLog {
  id?: string
  student_id: string
  week_number: number
  day_of_week: 'monday' | 'tuesday' | 'wednesday' | 'thursday' | 'friday' | 'saturday'
  date: string
  title: string
  activities: string
  tasks_completed?: string
  challenges_faced?: string
  skills_learned?: string
  hours_worked?: number
  supervisor_present: boolean
  supervisor_feedback?: string
  file_url?: string
  file_name?: string
  file_size?: number
  file_type?: string
  status: 'draft' | 'submitted'
  is_submitted: boolean
  submitted_at?: string
  created_at?: string
  updated_at?: string
}

export interface WeeklyReport {
  id?: string
  student_id: string
  week_number: number
  week_start_date: string
  week_end_date: string
  summary: string
  objectives_achieved?: string
  challenges_encountered?: string
  skills_developed?: string
  learning_outcomes?: string
  areas_for_improvement?: string
  next_week_goals?: string
  total_hours?: number
  attendance_days?: number
  self_assessment?: string
  self_rating?: number
  supervisor_comments?: string
  supervisor_rating?: number
  file_url?: string
  file_name?: string
  file_size?: number
  file_type?: string
  status: 'draft' | 'submitted' | 'reviewed' | 'approved' | 'needs_revision'
  is_submitted: boolean
  submitted_at?: string
  reviewed_at?: string
  supervisor_id?: string
  created_at?: string
  updated_at?: string
}

export interface DailyLogWithStudent extends DailyLog {
  student_name: string
  student_email: string
  first_name: string
  middle_name?: string
  last_name: string
  email: string
}

export interface WeeklyReportWithStudent extends WeeklyReport {
  student_name: string
  student_email: string
  first_name: string
  middle_name?: string
  last_name: string
  email: string
}

export interface LogbookWeek {
  week_number: number
  week_start_date: string
  week_end_date: string
  daily_logs: DailyLog[]
  weekly_report?: WeeklyReport
  completion_status: 'not_started' | 'in_progress' | 'completed'
  total_hours: number
  days_completed: number
}

export interface LogbookStatistics {
  total_daily_logs: number
  total_weekly_reports: number
  submitted_daily_logs: number
  submitted_weekly_reports: number
  active_students: number
  average_hours_per_day: number
  average_hours_per_week: number
  completion_rate: number
  current_week: number
}

export interface StudentProgress {
  student_id: string
  total_weeks: number
  completed_weeks: number
  current_week: number
  total_daily_logs: number
  submitted_daily_logs: number
  total_weekly_reports: number
  submitted_weekly_reports: number
  total_hours: number
  average_hours_per_day: number
  started_date: string
  last_activity: string
  weeks: LogbookWeek[]
}

export interface LogbookFilters {
  student_name?: string
  week_number?: number
  day_of_week?: string
  status?: 'all' | 'draft' | 'submitted' | 'reviewed' | 'approved' | 'needs_revision'
  date_from?: string
  date_to?: string
}

export interface FileUpload {
  file: File
  preview?: string
  progress: number
}

// Legacy interfaces for backward compatibility
export interface LogDay {
  id: string;
  title: string;
  status: string;
  hours: string;
  day_number: number;
  week_number: number;
  student_id?: string;
  created_at?: string;
  updated_at?: string;
}

export interface LogbookWeek {
  week_number: number;
  days: LogDay[];
  report: WeeklyReport;
}

export interface DayOption {
  value: number;
  label: string;
  disabled?: boolean;
}