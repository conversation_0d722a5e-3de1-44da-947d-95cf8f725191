import { DottedSeparator } from "@/components/dotted-separator";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, Card<PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { supervisor } from "@/features/auth/types/auth";
import { useForm } from "react-hook-form";
import { useCreateSupervisor } from "../api/use-create-supervisor";
import { useSupervisorFormModal } from "../hooks/use-supervisor-form-modal";
import { UseRetrieveSupervisor } from "../api/use-retrieve-supervisor";
import { useEffect } from "react";
import { useUpdateSupervisor } from "../api/use-update-supervisor";

interface SupervisorFormProps { 
    onCancel?: () => void;
}

export const SupervisorForm = ({onCancel} : SupervisorFormProps) => {
    const { close , id } = useSupervisorFormModal();
    const createSupervisor = useCreateSupervisor();
    const updateSupervisor = useUpdateSupervisor();
    const { data: response } = UseRetrieveSupervisor(id);
    const data = response ? response?.data[0] : null;

    const {
        register,
        handleSubmit,
        formState: { errors },
        watch,
        setValue
    } = useForm<supervisor>();

    useEffect(() => {
        if (id && data) {
            setValue("first_name", data.first_name || "");
            setValue("last_name", data.last_name || "");
            setValue("email", data.email || "");
            setValue("phone", data.phone || "");
        }
    }, [id, data, setValue]);

    const onSubmit = (data: supervisor) => {
        if(id){
            //edit mutation
            updateSupervisor.mutate({
                first_name: data.first_name || "",
                last_name: data.last_name || "",
                email: data.email || "",
                phone: data.phone || "",
                id: id || "",
            }, {
                onSuccess: () => {
                    close();
                },
            });
        }else{
            createSupervisor.mutate({
                first_name: data.first_name || "",
                last_name: data.last_name || "",
                email: data.email || "",
                phone: data.phone || "",
            }, {
                onSuccess: () => {
                    close();
                },
            });
        }
    };

    return (
        <Card className="w-full h-full border-none shadow-none">
                    <CardHeader className="flex p-7">
                        <CardTitle className="text-xl font-bold">
                            {id ? "Edit Supervisor" : "Create Supervisor"}
                        </CardTitle>
                    </CardHeader>
                    <div className="px-7">
                        <DottedSeparator />
                    </div>
                    <CardContent className="p-7">
            
                    <form onSubmit={handleSubmit(onSubmit)}>
                        <div className="flex flex-col gap-y-4">
                            <div className="flex justify-around gap-2 items-center">
                                <div className="grid gap-2">
                                    <Label htmlFor="fname">First Name</Label>
                                    <Input
                                        id="fname"
                                        type="text"
                                        autoFocus
                                        placeholder="first name"
                                        {...register("first_name", { required: "First Name is required" })}
                                    />
                                    {errors.first_name && (
                                    <p className="text-sm text-red-500">{errors.first_name.message}</p>
                                    )}
                                </div>
                                
                                <div className="grid gap-2">
                                    <Label htmlFor="lname">Last Name</Label>
                                    <Input
                                        id="lname"
                                        type="text"
                                        placeholder="Last name"
                                        {...register("last_name", { required: "Last Name is required" })}
                                    />
                                    {errors.last_name && (
                                    <p className="text-sm text-red-500">{errors.last_name.message}</p>
                                    )}
                                </div>
                            </div>

                            <div className="flex justify-around gap-2 items-center">
                                <div className="grid gap-2">
                                    <Label htmlFor="email">Email</Label>
                                    <Input
                                        id="email"
                                        type="text"
                                        placeholder="<EMAIL>"
                                        {...register("email", { required: "Email is required" })}
                                    />
                                    {errors.email && (
                                    <p className="text-sm text-red-500">{errors.email.message}</p>
                                    )}
                                </div>  
                                
                                <div className="grid gap-2">
                                    <Label htmlFor="phone">Phone Number</Label>
                                    <Input
                                        id="phone"
                                        type="text"
                                        placeholder="+255 ... ... ..."
                                        {...register("phone", { required: "Phone Number is required" })}
                                    />
                                    {errors.phone && (
                                    <p className="text-sm text-red-500">{errors.phone.message}</p>
                                    )}
                                </div>
                            </div>
                        
                        </div>
                        <DottedSeparator className="py-7" />
                        <div className="flex items-center justify-between">
                            <Button
                                type="button"
                                variant="secondary"
                                onClick={onCancel}
                                disabled={createSupervisor.isPending || updateSupervisor.isPending || false}
                            >
                                Cancel
                            </Button>
                            <Button 
                            disabled={createSupervisor.isPending || updateSupervisor.isPending}
                            >
                                {id ? "Update Supervisor" : "Create Supervisor"}
                            </Button>
                        </div>
                    </form>
            </CardContent>
        </Card>    )
}