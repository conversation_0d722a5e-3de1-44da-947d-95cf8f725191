import { DottedSeparator } from '@/components/dotted-separator';
import { But<PERSON> } from '@/components/ui/button';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import React from 'react'
import { useForm } from 'react-hook-form';
import { useGlobalManager } from '@/hooks/use-context';
import { useSubmitArrivalNote } from '../api/use-submit-arrival-note';
import { Arrivalnote } from '../types/arrival_note';
import { useArrivalNoteFormModal } from '../hooks/use-arrival-note-form-modal';

interface ArrivalNoteFormProps { 
    onCancel?: () => void;
}

type FormData = {
    student_phone : string;
    organization: string;
    i_supervisor_name: string;
    i_supervisor_phone: string;
    region: string;
    district: string;
    ward: string;
    street: string;
    house_number: string;
};

const ArrivalNoteForm = ({onCancel} : ArrivalNoteFormProps) => {
    const { user } = useGlobalManager();
    const arrivalNote = useSubmitArrivalNote();
    const { close } = useArrivalNoteFormModal();

    const {
            register,
            handleSubmit,
            formState: { errors },
            watch
        } = useForm<FormData>();

    const onSubmit = (data: FormData) => {
        const arrivalNoteData: Arrivalnote = {
            ...data,
            user_id: user.id,
            ipt_id: user.ipt_id
        }
        // console.log("arrivalNoteData", arrivalNoteData);
        arrivalNote.mutate(arrivalNoteData , {
            onSuccess: () => {
                close();
            },
        });
    }
    
  return (
    <Card className="w-full h-full border-none shadow-none">
            <CardHeader className="flex p-7">
                <CardTitle className="text-xl font-bold">
                    Submit Arrival Note
                </CardTitle>
            </CardHeader>
            <div className="px-7">
                <DottedSeparator />
            </div>
            <CardContent className="p-7">
            
                    <form onSubmit={handleSubmit(onSubmit)}>
                        <div className="grid grid-cols-1 gap-4">
                            <div className="grid gap-2">
                                <Label htmlFor="s_phone">Phone number</Label>
                                <Input
                                    id="s_phone"
                                    type="text"
                                    autoFocus
                                    placeholder="+255"
                                    {...register("student_phone", { required: "Student phone is required" })}
                                />
                                {errors.student_phone && (
                                <p className="text-sm text-red-500">{errors.student_phone.message}</p>
                                )}
                            </div>
                            <div className="grid gap-2">
                                <Label htmlFor="organization">Organization</Label>
                                <Input
                                    id="organization"
                                    type="text"
                                    placeholder="eg. University of Dar es Salaam"
                                    {...register("organization", { required: "Organization is required" })}
                                />
                                {errors.organization && (
                                <p className="text-sm text-red-500">{errors.organization.message}</p>
                                )}
                            </div>
                            <div className="grid grid-cols-2 gap-2">
                                <div className="col-span-1">
                                    <Label htmlFor="i_supervisor_name" className='mb-2'>Supervisor Name</Label>
                                    <Input
                                    id="i_supervisor_name"
                                    type="text"
                                    placeholder=""
                                    {...register("i_supervisor_name", { required: "Supervisor name is required" })}
                                    />
                                    {errors.i_supervisor_name && (
                                    <p className="text-sm text-red-500">{errors.i_supervisor_name.message}</p>
                                    )}
                                </div>

                                <div className="col-span-1">
                                    <Label htmlFor="i_supervisor_phone" className='mb-2'>Supervisor Phone number</Label>
                                    <Input
                                    id="i_supervisor_phone"
                                    type="text"
                                    placeholder="+255"
                                    {...register("i_supervisor_phone", { required: "Industrial supervisor phone number is required" })}
                                    />
                                    {errors.i_supervisor_phone && (
                                    <p className="text-sm text-red-500">{errors.i_supervisor_phone.message}</p>
                                    )}
                                </div>
                            </div>
                            <div className="grid grid-cols-3 gap-2">
                                <div className="col-span-1">
                                    <Label htmlFor="region" className='mb-2'>Region</Label>
                                    <Input
                                    id="region"
                                    type="text"
                                    placeholder=""
                                    {...register("region", { required: "Region is required" })}
                                    />
                                    {errors.region && (
                                    <p className="text-sm text-red-500">{errors.region.message}</p>
                                    )}
                                </div>

                                <div className="col-span-1">
                                    <Label htmlFor="district" className='mb-2'>District</Label>
                                    <Input
                                    id="district"
                                    type="text"
                                    placeholder="+255"
                                    {...register("district", { required: "District is required" })}
                                    />
                                    {errors.district && (
                                    <p className="text-sm text-red-500">{errors.district.message}</p>
                                    )}
                                </div>

                                <div className="col-span-1">
                                    <Label htmlFor="ward" className='mb-2'>Ward</Label>
                                    <Input
                                    id="ward"
                                    type="text"
                                    placeholder=""
                                    {...register("ward", { required: "Ward is required" })}
                                    />
                                    {errors.ward && (
                                    <p className="text-sm text-red-500">{errors.ward.message}</p>
                                    )}
                                </div>
                            </div>

                            <div className="grid grid-cols-2 gap-2">
                                <div className="grid-span-1">
                                    <Label htmlFor="street" className='mb-2'>Street</Label>
                                    <Input
                                    id="street"
                                    type="text"
                                    placeholder=""
                                    {...register("street", { required: "Street is required" })}
                                    />
                                    {errors.street && (
                                    <p className="text-sm text-red-500">{errors.street.message}</p>
                                    )}
                                </div>

                                <div className="grid-span-1">
                                    <Label htmlFor="house_number" className='mb-2'>House Number</Label>
                                    <Input
                                    id="house_number"
                                    type="text"
                                    placeholder=""
                                    {...register("house_number", { required: "House number is required" })}
                                    />
                                    {errors.house_number && (
                                    <p className="text-sm text-red-500">{errors.house_number.message}</p>
                                    )}
                                </div>
                            </div>

                        </div>
                        <DottedSeparator className="py-7" />
                        <div className="flex items-center justify-between">
                            <Button
                                type="button"
                                variant="secondary"
                                onClick={onCancel}
                                disabled={arrivalNote.isPending}
                            >
                                Cancel
                            </Button>
                            <Button 
                            disabled={arrivalNote.isPending || arrivalNote.isPending}
                            >
                                Submit
                            </Button>
                        </div>
                    </form>
            </CardContent>
        </Card>
  )
}

export default ArrivalNoteForm
