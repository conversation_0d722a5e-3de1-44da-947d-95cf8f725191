"use client"

import { ResponsiveModal } from "@/components/responsive-modal";
import { useSupervisorFormModal } from "../hooks/use-supervisor-form-modal";
import { SupervisorForm } from "./supervisor-form";


export const SupervisorFormModal = () => {
    const { isOpen, setIsOpen, close } = useSupervisorFormModal();

    return (
        <ResponsiveModal open={isOpen} onOpenChange={setIsOpen}>
            <SupervisorForm onCancel={() => close()} />
        </ResponsiveModal>
    )
}