"use client"

import { useState, useRef, useCallback, useEffect } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Separator } from "@/components/ui/separator"
import { 
  Bold, 
  Italic, 
  Underline, 
  List, 
  ListOrdered,
  AlignLeft,
  AlignCenter,
  AlignRight,
  Undo,
  Redo
} from "lucide-react"

interface RichTextEditorProps {
  value: string
  onChange: (value: string) => void
  placeholder?: string
  minHeight?: string
  maxLength?: number
  showCharacterCount?: boolean
}

export function RichTextEditor({
  value,
  onChange,
  placeholder = "Start writing your report...",
  minHeight = "300px",
  maxLength = 5000,
  showCharacterCount = true
}: RichTextEditorProps) {
  const editorRef = useRef<HTMLDivElement>(null)
  const [characterCount, setCharacterCount] = useState(0)
  const [isInitialized, setIsInitialized] = useState(false)

  // Initialize editor content only once to prevent cursor issues
  useEffect(() => {
    if (editorRef.current && !isInitialized && value) {
      editorRef.current.innerHTML = value
      setIsInitialized(true)
      const textContent = editorRef.current.textContent || ""
      setCharacterCount(textContent.length)
    }
  }, [value, isInitialized])

  const executeCommand = useCallback((command: string, value?: string) => {
    document.execCommand(command, false, value)
    if (editorRef.current) {
      editorRef.current.focus()
      handleContentChange()
    }
  }, [])

  const handleContentChange = useCallback(() => {
    if (editorRef.current) {
      const content = editorRef.current.innerHTML
      const textContent = editorRef.current.textContent || ""

      setCharacterCount(textContent.length)

      if (maxLength && textContent.length > maxLength) {
        // Prevent further input instead of truncating to maintain cursor position
        return
      }

      onChange(content)
    }
  }, [onChange, maxLength])

  const handlePaste = useCallback((e: React.ClipboardEvent) => {
    e.preventDefault()
    const text = e.clipboardData.getData('text/plain')
    document.execCommand('insertText', false, text)
  }, [])

  const handleKeyDown = useCallback((e: React.KeyboardEvent) => {
    if (maxLength && editorRef.current) {
      const textContent = editorRef.current.textContent || ""
      // Allow backspace, delete, and navigation keys even at max length
      const allowedKeys = ['Backspace', 'Delete', 'ArrowLeft', 'ArrowRight', 'ArrowUp', 'ArrowDown', 'Home', 'End']
      if (textContent.length >= maxLength && !allowedKeys.includes(e.key) && !e.ctrlKey && !e.metaKey) {
        e.preventDefault()
      }
    }
  }, [maxLength])

  const formatButtons = [
    { command: 'bold', icon: Bold, label: 'Bold' },
    { command: 'italic', icon: Italic, label: 'Italic' },
    { command: 'underline', icon: Underline, label: 'Underline' },
  ]

  const listButtons = [
    { command: 'insertUnorderedList', icon: List, label: 'Bullet List' },
    { command: 'insertOrderedList', icon: ListOrdered, label: 'Numbered List' },
  ]

  const alignButtons = [
    { command: 'justifyLeft', icon: AlignLeft, label: 'Align Left' },
    { command: 'justifyCenter', icon: AlignCenter, label: 'Align Center' },
    { command: 'justifyRight', icon: AlignRight, label: 'Align Right' },
  ]

  const historyButtons = [
    { command: 'undo', icon: Undo, label: 'Undo' },
    { command: 'redo', icon: Redo, label: 'Redo' },
  ]

  return (
    <div className="border rounded-lg overflow-hidden">
      {/* Toolbar */}
      <div className="border-b bg-muted/50 p-2">
        <div className="flex items-center space-x-1">
          {/* Format buttons */}
          {formatButtons.map((button) => (
            <Button
              key={button.command}
              variant="ghost"
              size="sm"
              onClick={() => executeCommand(button.command)}
              className="h-8 w-8 p-0"
              title={button.label}
            >
              <button.icon className="h-4 w-4" />
            </Button>
          ))}
          
          <Separator orientation="vertical" className="h-6" />
          
          {/* List buttons */}
          {listButtons.map((button) => (
            <Button
              key={button.command}
              variant="ghost"
              size="sm"
              onClick={() => executeCommand(button.command)}
              className="h-8 w-8 p-0"
              title={button.label}
            >
              <button.icon className="h-4 w-4" />
            </Button>
          ))}
          
          <Separator orientation="vertical" className="h-6" />
          
          {/* Alignment buttons */}
          {alignButtons.map((button) => (
            <Button
              key={button.command}
              variant="ghost"
              size="sm"
              onClick={() => executeCommand(button.command)}
              className="h-8 w-8 p-0"
              title={button.label}
            >
              <button.icon className="h-4 w-4" />
            </Button>
          ))}
          
          <Separator orientation="vertical" className="h-6" />
          
          {/* History buttons */}
          {historyButtons.map((button) => (
            <Button
              key={button.command}
              variant="ghost"
              size="sm"
              onClick={() => executeCommand(button.command)}
              className="h-8 w-8 p-0"
              title={button.label}
            >
              <button.icon className="h-4 w-4" />
            </Button>
          ))}
        </div>
      </div>
      
      {/* Editor */}
      <div
        ref={editorRef}
        contentEditable
        className="p-4 focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2"
        style={{
          minHeight,
          direction: 'ltr',
          textAlign: 'left',
          unicodeBidi: 'normal'
        }}
        onInput={handleContentChange}
        onPaste={handlePaste}
        onKeyDown={handleKeyDown}
        data-placeholder={placeholder}
        role="textbox"
        aria-label="Rich text editor for weekly report"
        aria-multiline="true"
        tabIndex={0}
        suppressContentEditableWarning={true}
      />
      
      {/* Character count */}
      {showCharacterCount && (
        <div className="border-t bg-muted/50 px-4 py-2 text-sm text-muted-foreground flex justify-between">
          <span>
            {characterCount} {maxLength && `/ ${maxLength}`} characters
          </span>
          {maxLength && characterCount > maxLength * 0.9 && (
            <span className={characterCount > maxLength ? "text-destructive" : "text-orange-500"}>
              {characterCount > maxLength ? "Character limit exceeded" : "Approaching character limit"}
            </span>
          )}
        </div>
      )}
      
      <style jsx>{`
        [contenteditable]:empty:before {
          content: attr(data-placeholder);
          color: #9ca3af;
          pointer-events: none;
        }
      `}</style>
    </div>
  )
}
