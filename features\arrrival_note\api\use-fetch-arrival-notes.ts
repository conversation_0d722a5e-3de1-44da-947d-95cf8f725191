import { useQuery } from "@tanstack/react-query";
import { toast } from "sonner";
import { ArrivalNote } from "../server/ArrivalNote";


export const useFetchArrivalNotes = () => {

    const query = useQuery({
        queryKey: ["arrival_notes"],
        queryFn: async () => {
            try {
                const res = await ArrivalNote.index();
                console.log('API =>',res.data)
                if (res.success) {
                    return res.data;
                }
                return [];
            } catch (error: any) {
                toast.error(error.message);
                return [];
            }
        },
    });
    return query;
}
