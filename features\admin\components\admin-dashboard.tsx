"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card"
import { Progress } from "@/components/ui/progress"
import { But<PERSON> } from "@/components/ui/button"
import { DottedSeparator } from "@/components/dotted-separator"
import { AdminDashboard as AdminDashboardService, AdminStatistics, RecentActivity } from "../server/AdminDashboard"
import { 
  User, 
  User2, 
  FileText, 
  ClipboardCheck, 
  BookOpen, 
  PieChart,
  BarChart3,
  Clock,
  TrendingUp
} from "lucide-react"
import Link from "next/link"

export function AdminDashboard() {
  const [statistics, setStatistics] = useState<AdminStatistics | null>(null)
  const [recentActivities, setRecentActivities] = useState<RecentActivity[]>([])
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    loadDashboardData()
  }, [])

  const loadDashboardData = async () => {
    try {
      setLoading(true)
      
      // Load statistics
      const statsResult = await AdminDashboardService.getStatistics()
      if (statsResult.success && statsResult.data) {
        setStatistics(statsResult.data)
      }

      // Load recent activities
      const activitiesResult = await AdminDashboardService.getRecentActivities()
      if (activitiesResult.success && activitiesResult.data) {
        setRecentActivities(activitiesResult.data)
      }

    } catch (error) {
      console.error('Failed to load dashboard data:', error)
    } finally {
      setLoading(false)
    }
  }

  const getLogbookProgress = () => {
    if (!statistics || statistics.total_daily_logs === 0) return 0
    return Math.round((statistics.submitted_daily_logs / statistics.total_daily_logs) * 100)
  }

  const getWeeklyReportProgress = () => {
    if (!statistics || statistics.total_weekly_reports === 0) return 0
    return Math.round((statistics.submitted_weekly_reports / statistics.total_weekly_reports) * 100)
  }

  const getFinalReportProgress = () => {
    if (!statistics || statistics.total_final_reports === 0) return 0
    return Math.round((statistics.submitted_final_reports / statistics.total_final_reports) * 100)
  }

  return (
    <>
      <div className="flex flex-col gap-4 px-4 lg:px-6">
        <div className="flex flex-col gap-2">
          <h1 className="text-2xl font-bold">Admin Dashboard</h1>
          <p className="text-muted-foreground">
            Administrative overview of the Industrial Practical Training Management System
          </p>
          <DottedSeparator />
        </div>
      </div>
      
      {/* Admin Analytics Dashboard */}
      <div className="grid gap-4 px-4 lg:px-6 md:grid-cols-2 lg:grid-cols-4">
        {/* Students Card */}
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Students</CardTitle>
            <User className="h-4 w-4 text-primary" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {loading ? "..." : statistics?.total_students || 0}
            </div>
            <p className="text-xs text-muted-foreground">
              {loading ? "Loading..." : `${statistics?.active_students || 0} active in logbooks`}
            </p>
            <Progress 
              value={loading ? 0 : (statistics?.active_students || 0) / (statistics?.total_students || 1) * 100} 
              className="mt-2" 
            />
          </CardContent>
        </Card>

        {/* Logbook Progress Card */}
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Logbook Progress</CardTitle>
            <BookOpen className="h-4 w-4 text-blue-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {loading ? "..." : `${getLogbookProgress()}%`}
            </div>
            <p className="text-xs text-muted-foreground">
              {loading ? "Loading..." : `${statistics?.submitted_daily_logs || 0}/${statistics?.total_daily_logs || 0} daily logs`}
            </p>
            <Progress value={loading ? 0 : getLogbookProgress()} className="mt-2" />
          </CardContent>
        </Card>

        {/* Weekly Reports Card */}
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Weekly Reports</CardTitle>
            <FileText className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {loading ? "..." : `${getWeeklyReportProgress()}%`}
            </div>
            <p className="text-xs text-muted-foreground">
              {loading ? "Loading..." : `${statistics?.submitted_weekly_reports || 0}/${statistics?.total_weekly_reports || 0} submitted`}
            </p>
            <Progress value={loading ? 0 : getWeeklyReportProgress()} className="mt-2" />
          </CardContent>
        </Card>

        {/* Current Week Card */}
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Current Week</CardTitle>
            <Clock className="h-4 w-4 text-orange-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {loading ? "..." : `Week ${statistics?.current_week || 1}`}
            </div>
            <p className="text-xs text-muted-foreground">
              {loading ? "Loading..." : `${statistics?.average_hours_per_day?.toFixed(1) || '0.0'}h avg/day`}
            </p>
            <Progress value={loading ? 0 : (statistics?.current_week || 1) / 10 * 100} className="mt-2" />
          </CardContent>
        </Card>
      </div>

      <DottedSeparator className="mx-4 lg:mx-6" />

      {/* Recent Activities Section */}
      <div className="px-4 lg:px-6 grid gap-4 md:grid-cols-2">
        <Card className="col-span-1">
          <CardHeader>
            <CardTitle>Recent Submissions</CardTitle>
            <CardDescription>Latest student activity in the system</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {loading ? (
                <div className="flex items-center justify-center h-32">
                  <div className="text-center">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
                    <p className="text-muted-foreground">Loading activities...</p>
                  </div>
                </div>
              ) : recentActivities.length === 0 ? (
                <div className="text-center py-8">
                  <FileText className="h-8 w-8 mx-auto mb-2 text-muted-foreground opacity-50" />
                  <p className="text-muted-foreground">No recent activities</p>
                </div>
              ) : (
                recentActivities.slice(0, 4).map((activity) => (
                  <div key={activity.id} className="flex items-center gap-4 border-b pb-3">
                    <div className="rounded-full bg-primary/10 p-2">
                      <FileText className="h-4 w-4 text-primary" />
                    </div>
                    <div className="flex-1">
                      <p className="text-sm font-medium">{activity.student_name} submitted {activity.type.replace('_', ' ')}</p>
                      <p className="text-xs text-muted-foreground">
                        {new Date(activity.submitted_at).toLocaleDateString()}
                      </p>
                    </div>
                    <Button variant="ghost" size="sm">View</Button>
                  </div>
                ))
              )}
            </div>
          </CardContent>
        </Card>

        <Card className="col-span-1">
          <CardHeader>
            <CardTitle>System Overview</CardTitle>
            <CardDescription>Key metrics and progress</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium">Final Reports</span>
              <span className="text-sm text-muted-foreground">
                {loading ? "..." : `${statistics?.submitted_final_reports || 0}/${statistics?.total_final_reports || 0}`}
              </span>
            </div>
            <Progress value={loading ? 0 : getFinalReportProgress()} />
            
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium">Daily Logs</span>
              <span className="text-sm text-muted-foreground">
                {loading ? "..." : `${getLogbookProgress()}%`}
              </span>
            </div>
            <Progress value={loading ? 0 : getLogbookProgress()} />
            
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium">Weekly Reports</span>
              <span className="text-sm text-muted-foreground">
                {loading ? "..." : `${getWeeklyReportProgress()}%`}
              </span>
            </div>
            <Progress value={loading ? 0 : getWeeklyReportProgress()} />
          </CardContent>
        </Card>
      </div>

      <div className="px-4 lg:px-6 mt-4">
        <Card>
          <CardHeader>
            <CardTitle>Quick Actions</CardTitle>
            <CardDescription>Frequently used administrative tasks</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid gap-4 md:grid-cols-3">
              <Link href="/admin/students">
                <Card className="hover:bg-accent cursor-pointer transition-colors">
                  <CardContent className="flex items-center p-6">
                    <User className="h-5 w-5 mr-3 text-primary" />
                    <div>
                      <h3 className="font-medium">Manage Students</h3>
                      <p className="text-sm text-muted-foreground">View and edit student records</p>
                    </div>
                  </CardContent>
                </Card>
              </Link>
              
              <Link href="/admin/reports/logbooks">
                <Card className="hover:bg-accent cursor-pointer transition-colors">
                  <CardContent className="flex items-center p-6">
                    <BookOpen className="h-5 w-5 mr-3 text-primary" />
                    <div>
                      <h3 className="font-medium">Logbook Management</h3>
                      <p className="text-sm text-muted-foreground">Monitor student logbooks</p>
                    </div>
                  </CardContent>
                </Card>
              </Link>
              
              <Link href="/admin/reports">
                <Card className="hover:bg-accent cursor-pointer transition-colors">
                  <CardContent className="flex items-center p-6">
                    <BarChart3 className="h-5 w-5 mr-3 text-primary" />
                    <div>
                      <h3 className="font-medium">Reports & Analytics</h3>
                      <p className="text-sm text-muted-foreground">View detailed reports</p>
                    </div>
                  </CardContent>
                </Card>
              </Link>
            </div>
          </CardContent>
        </Card>
      </div>
    </>
  )
}
