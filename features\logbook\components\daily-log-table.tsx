"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { DailyLog } from "../types/logbook"
import { useGlobalManager } from "@/hooks/use-context"
import {
  Plus,
  Edit,
  Save,
  X,
  Clock,
  Calendar,
  FileText,
  Trash2
} from "lucide-react"

interface DailyLogTableProps {
  weekNumber: number
  dailyLogs: DailyLog[]
  onSaveDailyLog: (dailyLog: DailyLog) => Promise<void>
  onDeleteDailyLog: (id: string) => Promise<void>
  loading?: boolean
}

const DAYS_OF_WEEK = [
  { value: 'monday', label: 'Monday' },
  { value: 'tuesday', label: 'Tuesday' },
  { value: 'wednesday', label: 'Wednesday' },
  { value: 'thursday', label: 'Thursday' },
  { value: 'friday', label: 'Friday' },
  { value: 'saturday', label: 'Saturday' }
]

export function DailyLogTable({
  weekNumber,
  dailyLogs,
  onSaveDailyLog,
  onDeleteDailyLog,
  loading = false
}: DailyLogTableProps) {
  const { user } = useGlobalManager()
  const [editingId, setEditingId] = useState<string | null>(null)
  const [newLogData, setNewLogData] = useState<Partial<DailyLog> | null>(null)
  const [saving, setSaving] = useState(false)

  // Get the next available day for new entries
  const getNextAvailableDay = () => {
    const existingDays = dailyLogs.map(log => log.day_of_week)
    const availableDay = DAYS_OF_WEEK.find(day => !existingDays.includes(day.value as any))
    return availableDay?.value as DailyLog['day_of_week'] || 'monday'
  }

  // Get current week's Monday date for date calculation
  const getWeekStartDate = () => {
    const now = new Date()
    const currentWeek = Math.ceil((now.getTime() - new Date(now.getFullYear(), 0, 1).getTime()) / (7 * 24 * 60 * 60 * 1000))
    const weekDiff = weekNumber - currentWeek
    const weekStart = new Date(now)
    weekStart.setDate(now.getDate() - now.getDay() + 1 + (weekDiff * 7)) // Monday of the week
    return weekStart
  }

  // Calculate date for a specific day of week
  const getDateForDay = (dayOfWeek: string) => {
    const weekStart = getWeekStartDate()
    const dayIndex = DAYS_OF_WEEK.findIndex(day => day.value === dayOfWeek)
    const date = new Date(weekStart)
    date.setDate(weekStart.getDate() + dayIndex)
    return date.toISOString().split('T')[0]
  }

  const handleAddDay = () => {
    if (dailyLogs.length >= 6) return // Maximum 6 days (Monday to Saturday)
    
    const nextDay = getNextAvailableDay()
    const newLog: Partial<DailyLog> = {
      student_id: user?.id || "",
      week_number: weekNumber,
      day_of_week: nextDay,
      date: getDateForDay(nextDay),
      title: "",
      activities: "",
      hours_worked: 8,
      supervisor_present: false,
      status: 'draft',
      is_submitted: false
    }
    setNewLogData(newLog)
    setEditingId('new')
  }

  const handleEdit = (log: DailyLog) => {
    setEditingId(log.id!)
    setNewLogData({ ...log })
  }

  const handleCancel = () => {
    setEditingId(null)
    setNewLogData(null)
  }

  const handleSave = async () => {
    if (!newLogData) return

    try {
      setSaving(true)
      const logData: DailyLog = {
        ...newLogData,
        student_id: user?.id || "",
        week_number: weekNumber,
        title: newLogData.title || "",
        activities: newLogData.activities || "",
        hours_worked: newLogData.hours_worked || 0,
        supervisor_present: newLogData.supervisor_present || false,
        status: newLogData.status || 'draft',
        is_submitted: newLogData.is_submitted || false
      } as DailyLog

      await onSaveDailyLog(logData)
      setEditingId(null)
      setNewLogData(null)
    } catch (error) {
      console.error('Failed to save daily log:', error)
    } finally {
      setSaving(false)
    }
  }

  const handleDelete = async (id: string) => {
    if (window.confirm('Are you sure you want to delete this daily log?')) {
      await onDeleteDailyLog(id)
    }
  }

  const updateNewLogData = (field: keyof DailyLog, value: any) => {
    if (!newLogData) return
    setNewLogData({ ...newLogData, [field]: value })
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            <FileText className="h-5 w-5" />
            Daily Logs - Week {weekNumber}
          </CardTitle>
          <Button 
            onClick={handleAddDay} 
            disabled={dailyLogs.length >= 6 || editingId !== null || loading}
            className="flex items-center gap-2"
          >
            <Plus className="h-4 w-4" />
            Add Day
          </Button>
        </div>
      </CardHeader>
      <CardContent>
        {loading ? (
          <div className="flex items-center justify-center h-32">
            <div className="text-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
              <p className="text-muted-foreground">Loading daily logs...</p>
            </div>
          </div>
        ) : (
          <div className="space-y-4">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead className="w-[120px]">Day</TableHead>
                  <TableHead>Content</TableHead>
                  <TableHead className="w-[100px]">Hours</TableHead>
                  <TableHead className="w-[100px]">Status</TableHead>
                  <TableHead className="w-[120px]">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {dailyLogs.map((log) => (
                  <TableRow key={log.id}>
                    <TableCell>
                      <div className="space-y-1">
                        <Badge variant="outline">
                          {DAYS_OF_WEEK.find(d => d.value === log.day_of_week)?.label}
                        </Badge>
                        <div className="text-xs text-muted-foreground">{log.date}</div>
                      </div>
                    </TableCell>
                    <TableCell>
                      {editingId === log.id ? (
                        <div className="space-y-2">
                          <Input
                            placeholder="Daily summary title"
                            value={newLogData?.title || ""}
                            onChange={(e) => updateNewLogData('title', e.target.value)}
                          />
                          <Textarea
                            placeholder="Describe your activities for this day..."
                            value={newLogData?.activities || ""}
                            onChange={(e) => updateNewLogData('activities', e.target.value)}
                            rows={3}
                          />
                        </div>
                      ) : (
                        <div className="space-y-1">
                          <div className="font-medium">{log.title}</div>
                          <div className="text-sm text-muted-foreground line-clamp-2">
                            {log.activities}
                          </div>
                        </div>
                      )}
                    </TableCell>
                    <TableCell>
                      {editingId === log.id ? (
                        <Input
                          type="number"
                          step="0.5"
                          min="0"
                          max="24"
                          value={newLogData?.hours_worked || 0}
                          onChange={(e) => updateNewLogData('hours_worked', parseFloat(e.target.value))}
                          className="w-20"
                        />
                      ) : (
                        <div className="flex items-center gap-1">
                          <Clock className="h-3 w-3" />
                          {log.hours_worked}h
                        </div>
                      )}
                    </TableCell>
                    <TableCell>
                      <Badge variant={log.is_submitted ? "default" : "secondary"}>
                        {log.is_submitted ? "Submitted" : "Draft"}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      {editingId === log.id ? (
                        <div className="flex items-center gap-1">
                          <Button
                            size="sm"
                            onClick={handleSave}
                            disabled={saving}
                            className="h-8 w-8 p-0"
                          >
                            <Save className="h-4 w-4" />
                          </Button>
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={handleCancel}
                            className="h-8 w-8 p-0"
                          >
                            <X className="h-4 w-4" />
                          </Button>
                        </div>
                      ) : (
                        <div className="flex items-center gap-1">
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => handleEdit(log)}
                            disabled={editingId !== null}
                            className="h-8 w-8 p-0"
                          >
                            <Edit className="h-4 w-4" />
                          </Button>
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => handleDelete(log.id!)}
                            disabled={editingId !== null}
                            className="h-8 w-8 p-0 text-destructive hover:text-destructive"
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      )}
                    </TableCell>
                  </TableRow>
                ))}

                {/* New log row */}
                {editingId === 'new' && newLogData && (
                  <TableRow>
                    <TableCell>
                      <div className="space-y-1">
                        <Select
                          value={newLogData.day_of_week}
                          onValueChange={(value) => {
                            updateNewLogData('day_of_week', value)
                            updateNewLogData('date', getDateForDay(value))
                          }}
                        >
                          <SelectTrigger className="w-full">
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            {DAYS_OF_WEEK.map((day) => (
                              <SelectItem
                                key={day.value}
                                value={day.value}
                                disabled={dailyLogs.some(log => log.day_of_week === day.value)}
                              >
                                {day.label}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                        <div className="text-xs text-muted-foreground">{newLogData.date}</div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="space-y-2">
                        <Input
                          placeholder="Daily summary title"
                          value={newLogData.title || ""}
                          onChange={(e) => updateNewLogData('title', e.target.value)}
                        />
                        <Textarea
                          placeholder="Describe your activities for this day..."
                          value={newLogData.activities || ""}
                          onChange={(e) => updateNewLogData('activities', e.target.value)}
                          rows={3}
                        />
                      </div>
                    </TableCell>
                    <TableCell>
                      <Input
                        type="number"
                        step="0.5"
                        min="0"
                        max="24"
                        value={newLogData.hours_worked || 0}
                        onChange={(e) => updateNewLogData('hours_worked', parseFloat(e.target.value))}
                        className="w-20"
                      />
                    </TableCell>
                    <TableCell>
                      <Badge variant="secondary">Draft</Badge>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-1">
                        <Button
                          size="sm"
                          onClick={handleSave}
                          disabled={saving || !newLogData.title || !newLogData.activities}
                          className="h-8 w-8 p-0"
                        >
                          <Save className="h-4 w-4" />
                        </Button>
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={handleCancel}
                          className="h-8 w-8 p-0"
                        >
                          <X className="h-4 w-4" />
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>

            {dailyLogs.length === 0 && editingId !== 'new' && (
              <div className="text-center py-8">
                <Calendar className="h-12 w-12 mx-auto mb-4 text-muted-foreground opacity-50" />
                <h3 className="text-lg font-medium mb-2">No daily logs for Week {weekNumber}</h3>
                <p className="text-muted-foreground mb-4">Start by adding your first daily log entry</p>
                <Button onClick={handleAddDay}>
                  <Plus className="h-4 w-4 mr-2" />
                  Add Day
                </Button>
              </div>
            )}
          </div>
        )}
      </CardContent>
    </Card>
  )
}
