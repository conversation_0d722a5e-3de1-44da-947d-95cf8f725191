'use client'

import { useState } from "react"
import { useFeedback } from "@/features/reports/hooks/use-reports"
import { FeedbackForm } from "@/features/reports/components/feedback-form"
import { FeedbackList } from "@/features/reports/components/feedback-list"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { <PERSON><PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { PlusCircle, MessageSquare, Star, FileText } from "lucide-react"
import { Feedback } from "@/features/reports/types/reports"

export default function FeedbackPage() {
  const {
    feedbackList,
    loading,
    saving,
    saveFeedback,
    submitFeedback,
    deleteFeedback,
    categories
  } = useFeedback()

  const [showFeedbackForm, setShowFeedbackForm] = useState(false)
  const [editingFeedback, setEditingFeedback] = useState<Feedback | null>(null)

  const handleAddFeedback = () => {
    setEditingFeedback(null)
    setShowFeedbackForm(true)
  }

  const handleEditFeedback = (feedback: Feedback) => {
    setEditingFeedback(feedback)
    setShowFeedbackForm(true)
  }

  const handleSaveFeedback = async (feedbackData: Feedback) => {
    try {
      if (editingFeedback) {
        await saveFeedback({ ...feedbackData, id: editingFeedback.id })
      } else {
        await saveFeedback(feedbackData)
      }
      setShowFeedbackForm(false)
      setEditingFeedback(null)
    } catch (error) {
      // Error handling is done in the hook
    }
  }

  const handleSubmitFeedback = async (feedbackData: Feedback) => {
    try {
      if (editingFeedback) {
        await submitFeedback({ ...feedbackData, id: editingFeedback.id })
      } else {
        await submitFeedback(feedbackData)
      }
      setShowFeedbackForm(false)
      setEditingFeedback(null)
    } catch (error) {
      // Error handling is done in the hook
    }
  }

  const handleDeleteFeedback = async (id: string) => {
    try {
      await deleteFeedback(id)
    } catch (error) {
      // Error handling is done in the hook
    }
  }

  if (loading) {
    return (
      <div className="h-full flex-1 flex-col space-y-4 p-4 md:space-y-8 md:p-8 flex">
        <div className="flex items-center justify-center h-40">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
            <p className="text-muted-foreground">Loading feedback...</p>
          </div>
        </div>
      </div>
    )
  }

  const submittedFeedback = feedbackList.filter(f => f.is_submitted)
  const draftFeedback = feedbackList.filter(f => !f.is_submitted)

  return (
    <div className="h-full flex-1 flex-col space-y-4 p-4 md:space-y-8 md:p-8 flex">
      <div className="flex items-center justify-between space-y-2">
        <div>
          <h2 className="text-2xl font-bold tracking-tight">Feedback & Recommendations</h2>
          <p className="text-muted-foreground">
            Share your internship experience and provide recommendations for program improvement
          </p>
        </div>
      </div>

      {/* Statistics Cards */}
      <div className="grid gap-4 md:grid-cols-3">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Feedback</CardTitle>
            <MessageSquare className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{feedbackList.length}</div>
            <p className="text-xs text-muted-foreground">
              Across all categories
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Submitted</CardTitle>
            <Star className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{submittedFeedback.length}</div>
            <p className="text-xs text-muted-foreground">
              Completed submissions
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Drafts</CardTitle>
            <FileText className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{draftFeedback.length}</div>
            <p className="text-xs text-muted-foreground">
              Saved drafts
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Main Content */}
      <Tabs defaultValue="feedback" className="w-full">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="feedback">My Feedback</TabsTrigger>
          <TabsTrigger value="categories">Categories</TabsTrigger>
        </TabsList>

        <TabsContent value="feedback" className="space-y-4">
          <div className="flex items-center justify-between">
            <h3 className="text-lg font-semibold">Your Feedback Submissions</h3>
            <Button onClick={handleAddFeedback} disabled={saving}>
              <PlusCircle className="mr-2 h-4 w-4" />
              Add Feedback
            </Button>
          </div>

          <FeedbackList
            feedbackList={feedbackList}
            onEdit={handleEditFeedback}
            onDelete={handleDeleteFeedback}
            loading={saving}
          />
        </TabsContent>

        <TabsContent value="categories" className="space-y-4">
          <h3 className="text-lg font-semibold">Feedback Categories</h3>
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
            {categories.map((category) => (
              <Card key={category.value} className="cursor-pointer hover:bg-accent transition-colors" onClick={handleAddFeedback}>
                <CardHeader>
                  <CardTitle className="text-base">{category.label}</CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-sm text-muted-foreground">{category.description}</p>
                  <div className="mt-3 text-xs text-primary">
                    {feedbackList.filter(f => f.category === category.value).length} feedback(s)
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>
      </Tabs>

      {/* Feedback Form Modal */}
      {showFeedbackForm && (
        <FeedbackForm
          isOpen={showFeedbackForm}
          onClose={() => {
            setShowFeedbackForm(false)
            setEditingFeedback(null)
          }}
          onSave={handleSaveFeedback}
          onSubmit={handleSubmitFeedback}
          initialData={editingFeedback || undefined}
          categories={categories}
          disabled={saving}
        />
      )}
    </div>
  )
}
