import { cn } from "@/lib/utils";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { useForm, SubmitHandler } from "react-hook-form";
import { useRegister } from "../api/use-register";
import { useFetchPrograms } from "@/features/programs/api/use-fetch-programs";
import { Select, SelectContent, SelectGroup, SelectItem, SelectLabel, SelectTrigger, SelectValue } from "@/components/ui/select";

type FormData = {
  email: string;
  password: string;
  fname: string;
  mname: string;
  lname: string;
  regno: string;
  class: string;
  program_id: string;
};

export function RegisterForm({
    setShowRegisterForm, 
    ipt_id,
    ...props
  }: {
    setShowRegisterForm: React.Dispatch<React.SetStateAction<boolean>>,
    ipt_id : string
  }) {

  const { data: programs } = useFetchPrograms();

  const programOptions = programs?.map((program : any) => ({
    value: program.id,
    label: program.name,
  }));

  const onSelectChange = (value: string) => {
    setValue('program_id', value); 
  };

  const signUp = useRegister();

  const {
    register,
    setValue,
    handleSubmit,
    formState: { errors },
    watch
  } = useForm<FormData>();

  const password = watch("password");
  const selectedProgram = watch('program_id');

  const passwordValidation = {
    required: "Password is required",
    minLength: {
      value: 8,
      message: "Password must be at least 8 characters long",
    },
    pattern: {
      value: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[!@#$%^&*])[A-Za-z\d!@#$%^&*]{8,}$/,
      message:
        "Password must contain at least one uppercase letter, one lowercase letter, one number, and one special character",
    },
  };

  const onSubmit: SubmitHandler<FormData> = (data) => {

    signUp.mutate(
      {
        email: data.email,
        password: data.password,
        fname: data.fname,
        mname: data.mname,
        lname: data.lname,
        regno: data.regno,
        class: data.class,
        program_id: data.program_id,
        ipt_id: ipt_id
      },
      {
        onSuccess: () => {
          // setAuthentication(true); 
        },
      }
    );
  };

  return (
    <div className={cn("flex flex-col gap-6")} {...props}>
      <Card>
        <CardHeader>
          <CardTitle className="text-2xl">Register</CardTitle>
          <CardDescription>
            Fill in the form below to create an account
          </CardDescription>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit(onSubmit)}>
            <div className="flex flex-col gap-6">

                {/* ipt field that is automatically selected */}
            <div className="flex gap-2">
              <div className="grid gap-2">
                <Label htmlFor="fname">First name</Label>
                <Input
                  id="fname"
                  type="text"
                  placeholder="Eric"
                  {...register("fname", { required: "First name is required" })}
                />
                {errors.fname && (
                  <p className="text-sm text-red-500">{errors.fname.message}</p>
                )}
              </div>
              <div className="grid gap-2">
                <Label htmlFor="mname">Middle name</Label>
                <Input
                  id="mname"
                  type="text"
                  placeholder="Ernest"
                  {...register("mname", { required: "Middle name is required" })}
                />
                {errors.mname && (
                  <p className="text-sm text-red-500">{errors.mname.message}</p>
                )}
              </div>
              <div className="grid gap-2">
                <Label htmlFor="lname">Last name</Label>
                <Input
                  id="lname"
                  type="text"
                  placeholder="Msilanga"
                  {...register("lname", { required: "lname is required" })}
                />
                {errors.lname && (
                  <p className="text-sm text-red-500">{errors.lname.message}</p>
                )}
              </div>
              </div>
              <div className="grid gap-2">
                <Label htmlFor="email">Registration Number</Label>
                <Input
                  id="regno"
                  type="number"
                  placeholder="***********"
                  {...register("regno", { required: "Registration number is required" })}
                />
                {errors.regno && (
                  <p className="text-sm text-red-500">{errors.regno.message}</p>
                )}
              </div>
              <div className="flex gap-2">
                <div className="grid gap-2">
                    <Label htmlFor="class">Class</Label>
                    <Input
                    id="class"
                    type="text"
                    placeholder="OD22IT"
                    {...register("class", { required: "Class is required" })}
                    />
                    {errors.class && (
                    <p className="text-sm text-red-500">{errors.class.message}</p>
                    )}
                </div>
                <div className="grid gap-2">
                    <Label htmlFor="program">Program</Label>
                    <Select value={selectedProgram} onValueChange={onSelectChange}>
                      <SelectTrigger className="w-[180px]">
                        <SelectValue placeholder="Select a program" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectGroup>
                          <SelectLabel>Programs</SelectLabel>
                          {programs && programOptions.map((program: any) =>(
                            <SelectItem value={program.value} key={program.value}>{program.label}</SelectItem>
                          ))}
                          
                        </SelectGroup>
                      </SelectContent>
                    </Select>
                    {errors.program_id && (
                      <p className="text-sm text-red-500">{errors.program_id.message}</p>
                    )}
                </div>
              </div>
               
              <div className="grid gap-2">
                <Label htmlFor="email">Email</Label>
                <Input
                  id="email"
                  type="email"
                  placeholder="<EMAIL>"
                  {...register("email", { required: "Email is required" })}
                />
                {errors.email && (
                  <p className="text-sm text-red-500">{errors.email.message}</p>
                )}
              </div>
              <div className="grid gap-2">
                <div className="flex items-center">
                  <Label htmlFor="password">Password</Label>
                  
                </div>
                <Input
                  id="password"
                  type="password"
                  {...register("password", passwordValidation)}
                />
                {errors.password && (
                  <p className="text-sm text-red-500">{errors.password.message}</p>
                )}
              </div>
             
              <Button type="submit" 
                      className="w-full" 
                      disabled={signUp.isPending}>
                {signUp.isPending ? "Registering..." : "Register"}
              </Button>
            </div>
            <div className="mt-4 text-center text-sm">
              Already have an account?{" "}
              <a
              href="#"
              className="underline"
              onClick={(e) => {
                e.preventDefault(); 
                setShowRegisterForm(false); 
              }}
            >
                Login
              </a>
            </div>
          </form>
        </CardContent>
      </Card>
    </div>
  );
}
