import { useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";
import { Auth } from "../server/Auth";
import { useGlobalManager } from "@/hooks/use-context";

type FormData = {
  email: string;
  password: string;
  fname: string;
  mname: string;
  lname: string;
  regno: string;
  class: string;
  program_id: string;
  ipt_id : string;
};

export const useRegister = () => {
  const queryClient = useQueryClient();
  const { setIsRegistered } = useGlobalManager()

  const mutation = useMutation<void, Error, FormData>({ 
    mutationFn: async (formData: FormData) => {
      const res = await Auth.register(formData);
      return res;
    },
    onSuccess: () => {
      setIsRegistered(true)
      queryClient.invalidateQueries({ queryKey: ["auth"] });
    },
    onError: (error) => {
      toast.error(error.message);
    },
  });

  return mutation;
};
