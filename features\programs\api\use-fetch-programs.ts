import { useQuery } from "@tanstack/react-query";
import { toast } from "sonner";
import { Program } from "../server/Program";


export const useFetchPrograms = () => {

    const query = useQuery({
        queryKey: ["programs"],
        queryFn: async () => {
            try {
                const res = await Program.index();
                if (res.success && res.data) {
                    return res.data;
                }
                return [];
            } catch (error: any) {
                toast.error(error.message);
                return [];
            }
        },
        // initialData: []
    });
    return query;
}
