"use client"

import { ResponsiveModal } from "@/components/responsive-modal";
import { useArrivalNoteFormModal } from "../hooks/use-arrival-note-form-modal";
import ArrivalNoteForm from "./arrival-note-form";


export const ArrivalNoteFormModal = () => {
    const { isOpen, setIsOpen, close } = useArrivalNoteFormModal();

    return (
        <ResponsiveModal open={isOpen} onOpenChange={setIsOpen}>
            <ArrivalNoteForm onCancel={() => close()} />
        </ResponsiveModal>
    )
}