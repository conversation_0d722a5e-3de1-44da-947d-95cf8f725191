import { useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";
import { Supervisor } from "../server/Supervisor";
import { supervisor } from "@/features/auth/types/auth";
import { useGlobalManager } from "@/hooks/use-context";

export const useCreateSupervisor = () => {
  const queryClient = useQueryClient();
  const { iptId: ipt_id } = useGlobalManager();

  const mutation = useMutation<supervisor, Error, { first_name: string , last_name: string , email: string , phone: string }>({ 
    mutationFn: async ({ first_name , last_name , email , phone }: { first_name: string , last_name: string , email: string , phone: string }) => {
      const res = await Supervisor.create(first_name , last_name , email , phone, ipt_id ?? "");
      return res;
    },
    onSuccess: () => {

        queryClient.invalidateQueries({ queryKey: ["supervisors"] });

    },

    onError: (error) => {
      toast.error(error.message);
    },
  });

  return mutation;
};
