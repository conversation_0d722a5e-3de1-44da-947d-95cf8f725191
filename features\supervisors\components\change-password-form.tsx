import { <PERSON><PERSON>Separator } from "@/components/dotted-separator";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { supervisor } from "@/features/auth/types/auth";
import { useForm } from "react-hook-form";
import { useEffect } from "react";
import { useChangePassword } from "../api/use-change-password";
import { useSupervisorFormModal } from "../hooks/use-supervisor-form-modal";

type Props = {
    newPassword?: string;
    confirmPassword?: string;
}

export const ChangePasswordForm = () => {

    const changePassword = useChangePassword();

    const { setIsChangePasswordOpen } = useSupervisorFormModal();
    

    const {
        register,
        handleSubmit,
        formState: { errors }
    } = useForm<Props>();

    const passwordValidation = {
        required: "Password is required",
        minLength: {
          value: 8,
          message: "Password must be at least 8 characters long",
        },
        pattern: {
          value: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[!@#$%^&*])[A-Za-z\d!@#$%^&*]{8,}$/,
          message:
            "Password must contain at least one uppercase letter, one lowercase letter, one number, and one special character",
        },
      };

    const onSubmit = (data: Props) => {
        if (data.newPassword) {
            changePassword.mutate(
                { newPassword: data.newPassword },
                {
                    onSuccess: () => {
                        setIsChangePasswordOpen(false);
                    }
                }
            );
        }
    };

    return (
        <Card className="w-full h-full border-none shadow-none">
                    <CardHeader className="flex p-7">
                        <CardTitle className="text-xl font-bold">
                           Create Your Password
                        </CardTitle>
                    </CardHeader>
                    <div className="px-7">
                        <DottedSeparator />
                    </div>
                    <CardContent className="p-7">
            
                    <form onSubmit={handleSubmit(onSubmit)}>
                        <div className="flex flex-col gap-y-4">
                                <div className="grid gap-2">
                                    <Label htmlFor="np">New Password</Label>
                                    <Input
                                        id="np"
                                        type="password"
                                        autoFocus
                                        placeholder="new password"
                                        {...register("newPassword", passwordValidation)}
                                    />
                                    {errors.newPassword && (
                                    <p className="text-sm text-red-500">{errors.newPassword.message}</p>
                                    )}
                                </div>
                                
                                <div className="grid gap-2">
                                    <Label htmlFor="lname">Confirm Password</Label>
                                    <Input
                                        id="lname"
                                        type="password"
                                        placeholder="confirm"
                                        {...register("confirmPassword", {
                                            required: "You need to confirm the password",
                                            validate: (value, formValues) =>
                                              value === formValues.newPassword || "Passwords do not match",
                                          })}                                    />
                                    {errors.confirmPassword && (
                                    <p className="text-sm text-red-500">{errors.confirmPassword.message}</p>
                                    )}
                                </div>

                        </div>
                        <DottedSeparator className="py-7" />
                        <div className="flex items-center justify-between">
                        
                            <Button 
                            disabled={changePassword.isPending || false}
                            >
                                Create Supervisor
                            </Button>
                        </div>
                    </form>
            </CardContent>
        </Card>    )
}