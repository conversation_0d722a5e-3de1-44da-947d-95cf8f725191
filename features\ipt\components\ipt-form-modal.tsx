"use client"

import { ResponsiveModal } from "@/components/responsive-modal";
import { useIptFormModal } from "../hooks/use-ipt-form-modal";
import IptForm from "./ipt-form";


export const IptFormModal = () => {
    const { isOpen, setIsOpen, close } = useIptFormModal();

    return (
        <ResponsiveModal open={isOpen} onOpenChange={setIsOpen}>
            <IptForm onCancel={() => close()} />
        </ResponsiveModal>
    )
}