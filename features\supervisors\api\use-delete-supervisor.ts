import { useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";
import { Supervisor } from "../server/Supervisor";
import { supervisor } from "@/features/auth/types/auth";
import { useGlobalManager } from "@/hooks/use-context";

export const useDeleteSupervisor = () => {
  const queryClient = useQueryClient();

  const mutation = useMutation<supervisor, Error, { id: string }>({ 
    mutationFn: async ({ id }: { id: string }) => {
      const res = await Supervisor.destroy(id);
      return res;
    },
    onSuccess: () => {

        queryClient.invalidateQueries({ queryKey: ["supervisors"] });

    },

    onError: (error) => {
      toast.error(error.message);
    },
  });

  return mutation;
};
